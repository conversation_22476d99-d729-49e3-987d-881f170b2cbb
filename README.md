# 🧠 Agent Lee - Pure Frontend AI System

Agent <PERSON> is a **100% frontend-only AI cognitive system** featuring a 3D interactive brain interface with six specialized hemispheres, each powered by browser-native LLM models including your custom Absolute Zero Reasoner.

## ✅ **Zero Backend Required**
- **No Python servers** - Everything runs in browser
- **No API keys** - All models run locally
- **No installation** - Just open `index.html`
- **True intelligence** - 5 LLM models including custom AZR

## 🚀 **Quick Start**
```bash
# That's it! Just open the file in any modern browser
open index.html
```

## 🧠 **5 Browser-Native LLM Models**
1. **🟡 PHI-3 Mini (128K)** - Chat, reasoning, RAG operations
2. **🦙 Llama 3.2-1B** - General chat and database operations
3. **💎 Gemma 2B** - Agent operations and echo generation
4. **🔍 MiniLM Embedder** - Semantic search and RAG retrieval
5. **🎯 Absolute Zero Reasoner** - Your custom GGUF model via Wllama
