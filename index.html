<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Lee - Cognitive Core</title>
    
    <!-- Core Libraries -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/interactjs/dist/interact.min.js"></script>
    
    <!-- 3D and Visualization -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    
    <!-- Icons and Fonts -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;500;700;900&display=swap" rel="stylesheet">

    <!-- Production Infrastructure -->
    <script src="production_monitoring.js"></script>
    <script src="automated_test_suite.js"></script>
    <script src="schema_migration_system.js"></script>

    <!-- Agent Lee Core Modules -->
    <script src="cognitive-matrix.js"></script>
    <script src="agentMemory.js"></script>
    <script src="agentStatus.js"></script>
    <script src="syncData.js"></script>
    <script src="systemHealth.js"></script>
    <script src="cameraDiagnostics.js"></script>

    <!-- Streamlined LLM System with Wllama -->
    <script type="module">
        // Import and initialize the streamlined LLM manager
        console.log('🔄 Loading Streamlined LLM Manager...');

        try {
            // Import the manager and make it globally available
            const module = await import('./frontend/JS/streamlined-llm-manager.js');
            console.log('✅ Streamlined LLM Manager module loaded');

            // The manager should already be available as window.StreamlinedLLMManager
            // from the module itself, but let's ensure it's there
            if (window.StreamlinedLLMManager) {
                console.log('✅ StreamlinedLLMManager available globally');
            } else {
                console.warn('⚠️ StreamlinedLLMManager not found on window object');
            }
        } catch (error) {
            console.error('❌ Failed to load Streamlined LLM Manager:', error);
        }
    </script>

    <!-- Agent Lee Card Integration -->
    <script src="frontend/JS/agent-lee-card-integration.js"></script>
    
    <style>
        :root {
            /* Agent Lee Brand Colors */
            --bg-gradient-1: #0a0e29;
            --bg-gradient-2: #1a1040;
            --bg-gradient-3: #0f0520;
            --primary-color: #00f2ff;
            --primary-glow: rgba(0, 242, 255, 0.6);
            --secondary-color: #9854ff;
            --accent-color: #ff2a6d;
            --accent-glow: rgba(255, 42, 109, 0.6);
            --text-color: #e0e7ff;
            --success-color: #1bf7cd;
            --warning-color: #f7d31b;
            --danger-color: #f73a1b;
            
            /* UI Components */
            --panel-bg: rgba(16, 20, 40, 0.4);
            --panel-border: rgba(0, 242, 255, 0.15);
            --card-shadow: 0 20px 60px rgba(3, 4, 16, 0.4);
            --card-glow: 0 0 40px rgba(108, 71, 255, 0.2);
            --transition: all 0.4s cubic-bezier(0.17, 0.84, 0.44, 1);
            
            /* Theme Variables */
            --theme-bg: var(--bg-gradient-1);
            --theme-text: var(--text-color);
            --theme-card: rgba(15, 23, 42, 0.95);
            --theme-border: #3b82f6;
        }
        
        /* Light Mode */
        [data-theme="light"] {
            --theme-bg: #f8fafc;
            --theme-text: #1e293b;
            --theme-card: rgba(255, 255, 255, 0.95);
            --theme-border: #3b82f6;
            --bg-gradient-1: #f1f5f9;
            --bg-gradient-2: #e2e8f0;
            --bg-gradient-3: #cbd5e1;
            --text-color: #1e293b;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--bg-gradient-1), var(--bg-gradient-2), var(--bg-gradient-3));
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            color: var(--text-color);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }
        
        @keyframes gradientBG {
            0% { background-position: 0% 50% }
            50% { background-position: 100% 50% }
            100% { background-position: 0% 50% }
        }
        
        /* Background Effects */
        .background-effect {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 0;
            overflow: hidden;
        }
        
        .holographic-grid {
            position: absolute;
            width: 200%;
            height: 200%;
            top: -50%;
            left: -50%;
            background-image: 
                linear-gradient(rgba(0, 242, 255, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 242, 255, 0.05) 1px, transparent 1px);
            background-size: 40px 40px;
            transform: perspective(500px) rotateX(60deg);
            animation: gridMove 20s linear infinite;
            opacity: 0.3;
        }
        
        @keyframes gridMove {
            0% { background-position: 0 0; }
            100% { background-position: 0 40px; }
        }
        
        /* Floating Agent Card - Fixed Position */
        .agent-card {
            position: fixed;
            top: 50px;
            right: 50px;
            width: 360px;
            height: auto;
            max-height: 90vh;
            background: var(--theme-card);
            -webkit-backdrop-filter: blur(20px);
            backdrop-filter: blur(20px);
            border: 2px solid var(--theme-border);
            border-radius: 20px;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(59, 130, 246, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            z-index: 9999;
            -webkit-user-select: none;
            user-select: none;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .agent-card.minimized {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            max-height: 60px;
            overflow: hidden;
            right: 20px;
            bottom: 20px;
            top: auto;
        }
        
        .agent-card.minimized .card-content {
            display: none;
        }
        
        .agent-card.minimized .card-header {
            padding: 0;
            justify-content: center;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            border: none;
        }
        
        .agent-card.minimized .agent-avatar {
            width: 40px;
            height: 40px;
        }
        
        .agent-card.minimized .agent-info,
        .agent-card.minimized .card-controls {
            display: none;
        }
        
        .agent-card:hover {
            transform: translateY(-2px);
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(59, 130, 246, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        /* Card Header */
        .card-header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            padding: 16px 20px;
            cursor: grab;
            display: flex;
            align-items: center;
            gap: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .card-header:active {
            cursor: grabbing;
        }
        
        .agent-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .agent-avatar:hover {
            transform: scale(1.05);
        }
        
        .agent-info h3 {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
            font-family: 'Orbitron', monospace;
        }
        
        .agent-info p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
        }
        
        .card-controls {
            margin-left: auto;
            display: flex;
            gap: 8px;
        }
        
        .control-btn {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 14px;
        }
        
        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }
        
        .control-btn.talking {
            background: var(--danger-color);
            animation: pulse 1s infinite;
        }
        
        .control-btn.recording {
            background: var(--accent-color);
            animation: pulse 1s infinite;
        }
        
        .control-btn.minimize {
            background: var(--warning-color);
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        /* Chat Area */
        .chat-area {
            height: 200px;
            background: rgba(15, 23, 42, 0.8);
            margin: 16px;
            border-radius: 12px;
            border: 1px solid rgba(75, 85, 99, 0.3);
            overflow-y: auto;
            padding: 12px;
            position: relative;
        }
        
        [data-theme="light"] .chat-area {
            background: rgba(248, 250, 252, 0.8);
            border-color: rgba(203, 213, 225, 0.5);
        }
        
        .chat-message {
            background: rgba(59, 130, 246, 0.9);
            color: white;
            padding: 10px 14px;
            border-radius: 12px;
            margin-bottom: 10px;
            font-size: 14px;
            line-height: 1.4;
            max-width: 85%;
            animation: slideIn 0.3s ease;
        }
        
        .chat-message.user {
            background: var(--accent-color);
            margin-left: auto;
            text-align: right;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .empty-chat {
            color: rgba(255, 255, 255, 0.5);
            text-align: center;
            font-style: italic;
            font-size: 13px;
            margin-top: 80px;
        }
        
        [data-theme="light"] .empty-chat {
            color: rgba(30, 41, 59, 0.5);
        }
        
        /* Input Area */
        .input-area {
            padding: 16px;
            background: rgba(30, 41, 59, 0.5);
            border-top: 1px solid rgba(75, 85, 99, 0.3);
        }
        
        [data-theme="light"] .input-area {
            background: rgba(248, 250, 252, 0.8);
            border-color: rgba(203, 213, 225, 0.5);
        }
        
        .input-wrapper {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .message-input {
            flex: 1;
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 8px;
            padding: 10px 14px;
            color: white;
            font-size: 14px;
            resize: none;
            outline: none;
            transition: border-color 0.2s ease;
        }
        
        [data-theme="light"] .message-input {
            background: white;
            color: var(--text-color);
            border-color: rgba(203, 213, 225, 0.5);
        }
        
        .message-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }
        
        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }
        
        [data-theme="light"] .message-input::placeholder {
            color: rgba(30, 41, 59, 0.4);
        }
        
        .send-btn {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 16px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .send-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        /* Navigation Grid */
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            padding: 16px;
            background: rgba(30, 41, 59, 0.5);
        }
        
        [data-theme="light"] .nav-grid {
            background: rgba(248, 250, 252, 0.8);
        }
        
        .nav-btn {
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 12px;
            padding: 12px 8px;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            font-weight: 500;
            min-height: 70px;
            justify-content: center;
        }
        
        [data-theme="light"] .nav-btn {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            color: var(--text-color);
            border-color: rgba(203, 213, 225, 0.5);
        }
        
        .nav-btn:hover {
            background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        [data-theme="light"] .nav-btn:hover {
            background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
        }
        
        .nav-btn .icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        /* Floating GUIs - Fullscreen Support */
        .floating-gui {
            position: fixed;
            background: rgba(15, 23, 42, 0.95);
            border: 1px solid var(--primary-color);
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4),
                       0 0 15px rgba(0, 242, 255, 0.3);
            z-index: 10000;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            max-width: 90vw;
            max-height: 90vh;
            -webkit-backdrop-filter: blur(20px);
            backdrop-filter: blur(20px);
            touch-action: none;
        }
        
        .floating-gui.fullscreen {
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            max-width: 100vw;
            max-height: 100vh;
            border-radius: 0;
            z-index: 10001;
        }
        
        .floating-gui-header {
            background: linear-gradient(90deg, rgba(0, 30, 60, 0.9), rgba(0, 50, 90, 0.9));
            padding: 12px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: move;
            -webkit-user-select: none;
            user-select: none;
            flex-shrink: 0;
            touch-action: none;
        }
        
        .floating-gui.fullscreen .floating-gui-header {
            cursor: default;
        }
        
        .floating-gui-title {
            color: var(--primary-color);
            font-family: 'Orbitron', sans-serif;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .floating-gui-controls {
            display: flex;
            gap: 8px;
        }
        
        .floating-gui-btn {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: rgba(0, 0, 0, 0.7);
            transition: all 0.2s ease;
        }
        
        .floating-gui-btn:hover {
            transform: scale(1.1);
        }
        
        .floating-gui-fullscreen {
            background-color: var(--success-color);
        }
        
        .floating-gui-minimize {
            background-color: var(--warning-color);
        }
        
        .floating-gui-close {
            background-color: var(--danger-color);
        }
        
        .floating-gui-content {
            flex: 1;
            overflow: hidden;
            position: relative;
        }
        
        .floating-gui-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        /* Resize Handle for Floating GUIs */
        .resize-handle {
            position: absolute;
            width: 20px;
            height: 20px;
            bottom: 0;
            right: 0;
            cursor: nwse-resize;
            background: var(--primary-color);
            opacity: 0.3;
            border-radius: 0 0 12px 0;
            transition: opacity 0.2s ease;
        }
        
        .resize-handle:hover {
            opacity: 0.6;
        }
        
        .floating-gui.fullscreen .resize-handle {
            display: none;
        }
        
        /* Interface Containers */
        .interface-container {
            width: 100%;
            height: 100%;
            background: rgba(15, 23, 42, 0.8);
            border-radius: 8px;
            padding: 20px;
            overflow-y: auto;
        }
        
        /* Settings GUI with LLM Center */
        .settings-gui {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .settings-options {
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            border-bottom: 1px solid rgba(0, 242, 255, 0.15);
            flex-shrink: 0;
        }
        
        .settings-panel {
            background: rgba(16, 20, 40, 0.4);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(0, 242, 255, 0.15);
        }
        
        .settings-panel h3 {
            color: var(--primary-color);
            margin-bottom: 16px;
            font-family: 'Orbitron', monospace;
        }
        
        .llm-center {
            flex: 1;
            background: linear-gradient(180deg, rgba(10, 14, 41, 0.8), rgba(15, 5, 32, 0.8));
            overflow: hidden;
            min-height: 600px;
            position: relative;
        }
        
        .llm-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        /* Minimized GUIs Tray */
        .minimized-tray {
            position: fixed;
            bottom: 20px;
            left: 20px;
            display: flex;
            gap: 10px;
            z-index: 9998;
        }
        
        .minimized-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--theme-card);
            border: 2px solid var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--primary-color);
            font-size: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }
        
        .minimized-icon:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
        }
        
        /* ===== UNIFIED DASHBOARD STYLES ===== */

        /* Navigation Styles */
        #unified-nav {
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            height: 100vh;
            background: linear-gradient(180deg, var(--bg-gradient-1), var(--bg-gradient-2));
            border-right: 2px solid var(--primary-color);
            z-index: 10000;
            display: flex;
            flex-direction: column;
            box-shadow: 0 0 30px rgba(0, 242, 255, 0.3);
        }

        .nav-header {
            padding: 20px;
            border-bottom: 1px solid var(--panel-border);
            background: rgba(16, 20, 40, 0.6);
        }

        .nav-header h2 {
            color: var(--primary-color);
            font-family: 'Orbitron', sans-serif;
            font-size: 18px;
            margin-bottom: 10px;
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .nav-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: var(--text-color);
        }

        .nav-buttons {
            flex: 1;
            padding: 20px 0;
            overflow-y: auto;
        }

        .nav-btn {
            width: 100%;
            background: transparent;
            border: none;
            color: var(--text-color);
            padding: 15px 20px;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 14px;
            border-left: 3px solid transparent;
        }

        .nav-btn:hover {
            background: rgba(0, 242, 255, 0.1);
            border-left-color: var(--primary-color);
            color: var(--primary-color);
        }

        .nav-btn.active {
            background: rgba(0, 242, 255, 0.2);
            border-left-color: var(--primary-color);
            color: var(--primary-color);
            box-shadow: inset 0 0 20px rgba(0, 242, 255, 0.1);
        }

        .nav-btn i {
            width: 20px;
            text-align: center;
        }

        /* Dashboard Main Area */
        #unified-dashboard {
            margin-left: 250px;
            height: 100vh;
            overflow: hidden;
        }

        .dashboard-panel {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--theme-bg);
        }

        .dashboard-panel.hidden {
            display: none;
        }

        .hidden {
            display: none;
        }

        .panel-header {
            background: linear-gradient(90deg, rgba(0, 30, 60, 0.9), rgba(0, 50, 90, 0.9));
            padding: 20px 30px;
            border-bottom: 2px solid var(--primary-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 20px rgba(0, 242, 255, 0.3);
        }

        .panel-header h2 {
            color: var(--primary-color);
            font-family: 'Orbitron', sans-serif;
            font-size: 24px;
            text-shadow: 0 0 15px var(--primary-glow);
        }

        .panel-status {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .status-badge {
            background: var(--success-color);
            color: #000;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.active {
            animation: pulse 2s infinite;
        }

        .panel-content {
            flex: 1;
            overflow: hidden;
            position: relative;
        }

        .panel-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
        }

        /* Hemisphere Status Indicators */
        .hemisphere-status {
            display: flex;
            gap: 8px;
        }

        .hemisphere-indicator {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .hemisphere-indicator.phi3 {
            background: #FFD700;
            color: #000;
        }

        .hemisphere-indicator.gemini {
            background: #FF8C00;
            color: #fff;
        }

        .hemisphere-indicator.qwen {
            background: #20B2AA;
            color: #fff;
        }

        .hemisphere-indicator.llama {
            background: #87CEEB;
            color: #000;
        }

        .hemisphere-indicator.echo {
            background: #FF4500;
            color: #fff;
        }

        .hemisphere-indicator.azr {
            background: #4B0082;
            color: #fff;
        }

        .hemisphere-indicator.active {
            border-color: #fff;
            box-shadow: 0 0 15px currentColor;
            animation: pulse 2s infinite;
        }

        /* Brain Visualization */
        .brain-visualization {
            height: 70%;
            background: linear-gradient(45deg, rgba(0, 0, 0, 0.2), rgba(0, 242, 255, 0.02));
            border-radius: 15px;
            margin: 20px;
            overflow: hidden;
            border: 1px solid var(--panel-border);
        }

        .brain-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .core-controls {
            height: 30%;
            padding: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        .core-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: #fff;
            padding: 15px 25px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .core-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 242, 255, 0.4);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            #unified-nav {
                width: 100%;
                height: auto;
                position: relative;
            }

            #unified-dashboard {
                margin-left: 0;
            }

            .nav-buttons {
                display: flex;
                overflow-x: auto;
                padding: 10px;
            }

            .nav-btn {
                min-width: 120px;
                text-align: center;
                flex-direction: column;
                gap: 5px;
                padding: 10px;
            }

            .agent-card {
                width: 90vw;
                right: 5vw;
                top: 20px;
            }

            .nav-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .floating-gui {
                max-width: 95vw;
                max-height: 90vh;
            }

            .settings-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body data-theme="dark">
    <div class="background-effect">
        <div class="holographic-grid"></div>
    </div>
    
    <div id="root"></div>

    <!-- Unified Dashboard Navigation -->
    <nav id="unified-nav" class="hidden">
        <div class="nav-header">
            <h2>🧠 Agent Lee Cognitive Core</h2>
            <div class="nav-status">
                <span class="status-indicator"></span>
                <span>All Systems Online</span>
            </div>
        </div>
        <div class="nav-buttons">
            <button class="nav-btn active" onclick="showPanel('panel-core')">
                <i class="fas fa-brain"></i>
                <span>Cognitive Core</span>
            </button>
            <button class="nav-btn" onclick="showPanel('panel-todo')">
                <i class="fas fa-tasks"></i>
                <span>PHI-3 To-Do</span>
            </button>
            <button class="nav-btn" onclick="showPanel('panel-agents')">
                <i class="fas fa-users-cog"></i>
                <span>GEMINI Agents</span>
            </button>
            <button class="nav-btn" onclick="showPanel('panel-workers')">
                <i class="fas fa-cogs"></i>
                <span>QWEN Workers</span>
            </button>
            <button class="nav-btn" onclick="showPanel('panel-db')">
                <i class="fas fa-database"></i>
                <span>LLAMA Database</span>
            </button>
            <button class="nav-btn" onclick="showPanel('panel-logs')">
                <i class="fas fa-chart-line"></i>
                <span>ECHO Logs</span>
            </button>
        </div>
    </nav>

    <!-- Unified Dashboard Panels -->
    <main id="unified-dashboard" class="hidden">
        <!-- Cognitive Core Panel (Default) -->
        <section id="panel-core" class="dashboard-panel">
            <div class="panel-header">
                <h2>🧠 Agent Lee Cognitive Core</h2>
                <div class="hemisphere-status">
                    <div class="hemisphere-indicator phi3" title="PHI-3 To-Do">PHI</div>
                    <div class="hemisphere-indicator gemini" title="GEMINI Agents">GEM</div>
                    <div class="hemisphere-indicator qwen" title="QWEN Workers">QWN</div>
                    <div class="hemisphere-indicator llama" title="LLAMA Database">LLM</div>
                    <div class="hemisphere-indicator echo" title="ECHO Logs">ECH</div>
                    <div class="hemisphere-indicator azr active" title="AZR LLM Core">AZR</div>
                </div>
            </div>
            <div class="panel-content">
                <div class="brain-visualization">
                    
                </div>
                <div class="core-controls">
                    <button class="core-btn" onclick="testStructuredOutput()">
                        <i class="fas fa-code"></i>
                        Test Structured Output
                    </button>
                    <button class="core-btn" onclick="pulseAllHemispheres()">
                        <i class="fas fa-brain"></i>
                        Pulse All Hemispheres
                    </button>
                    <button class="core-btn" onclick="initializeAllSystems()">
                        <i class="fas fa-power-off"></i>
                        Initialize All Systems
                    </button>
                </div>
            </div>
        </section>

        <!-- PHI-3 To-Do Panel -->
        <section id="panel-todo" class="dashboard-panel hidden">
            <div class="panel-header">
                <h2>📋 PHI-3 To-Do System</h2>
                <div class="panel-status">
                    <span class="status-badge active">Active</span>
                    <span class="task-count">0 Tasks</span>
                </div>
            </div>
            <div class="panel-content">
                
            </div>
        </section>

        <!-- GEMINI Agents Panel -->
        <section id="panel-agents" class="dashboard-panel hidden">
            <div class="panel-header">
                <h2>🤖 GEMINI Agent Operations</h2>
                <div class="panel-status">
                    <span class="status-badge active">Online</span>
                    <span class="agent-count">0 Agents</span>
                </div>
            </div>
            <div class="panel-content">
                
            </div>
        </section>

        <!-- QWEN Workers Panel -->
        <section id="panel-workers" class="dashboard-panel hidden">
            <div class="panel-header">
                <h2>⚙️ QWEN Worker Grid</h2>
                <div class="panel-status">
                    <span class="status-badge active">Running</span>
                    <span class="worker-count">0 Workers</span>
                </div>
            </div>
            <div class="panel-content">
                
            </div>
        </section>

        <!-- LLAMA Database Panel -->
        <section id="panel-db" class="dashboard-panel hidden">
            <div class="panel-header">
                <h2>🗄️ LLAMA Database Health</h2>
                <div class="panel-status">
                    <span class="status-badge active">Healthy</span>
                    <span class="db-status">Connected</span>
                </div>
            </div>
            <div class="panel-content">
                
            </div>
        </section>

        <!-- ECHO Logs Panel -->
        <section id="panel-logs" class="dashboard-panel hidden">
            <div class="panel-header">
                <h2>📊 ECHO System Logs</h2>
                <div class="panel-status">
                    <span class="status-badge active">Monitoring</span>
                    <span class="log-count">0 Events</span>
                </div>
            </div>
            <div class="panel-content">
                
            </div>
        </section>
    </main>
    
    <!-- Schemas JSON Definition for Agent Lee -->
    <script type="application/schema+json" id="agent-lee-schemas">
    {
        "llmInitializationState": {
            "sessionID": "",
            "bootTimestamp": "",
            "contextLoaded": false,
            "initVectorHash": "",
            "llmRoleActivated": "",
            "linkedAgents": []
        },
        "userSentimentProfile": {
            "userID": "",
            "emotionalTone": "neutral",
            "recentKeywords": [],
            "interactionScore": 0.0,
            "volatilityScore": 0.0,
            "lastUpdated": ""
        },
        "agentSpeechPattern": {
            "agentName": "Agent Lee",
            "speechTone": "charming",
            "defaultGreeting": "Welcome back, ready to create something amazing today?",
            "fallbackPhrase": "Let me walk you through that.",
            "signatureClosure": "All clear. Let me know when you're ready for the next step.",
            "usesPauseBeforeReply": true,
            "mirrorsUserTone": true
        },
        "workerProfile": {
            "workerID": "",
            "role": "agent",
            "assignedTools": [],
            "taskCount": 0,
            "successfulCompletions": 0,
            "errorRate": 0.0,
            "lastCheckIn": "",
            "performanceTier": "C"
        }
    }
    </script>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        // Initialize Agent Lee Schemas
        window.AgentLeeSchemas = window.AgentLeeSchemas || {};

        // Load schema from embedded JSON
        const loadAgentLeeSchemas = () => {
            try {
                const schemaElement = document.getElementById('agent-lee-schemas');
                const schemas = JSON.parse(schemaElement.textContent);
                Object.assign(window.AgentLeeSchemas, schemas);
                console.log('✅ Agent Lee Schemas Loaded:', Object.keys(window.AgentLeeSchemas));
            } catch (err) {
                console.error('❌ Failed to load Agent Lee Schemas:', err);
            }
        };

        // ===== AGENT LEE COGNITIVE CORE SYSTEM =====
        // Brain Hemisphere Configuration with Color-Coded Regions
        const BRAIN_HEMISPHERES = {
            phi3: {
                name: "PHI-3 (To-Do)",
                function: "To-Do List & Workflow",
                color: "#FFD700", // Yellow
                position: { x: -0.8, y: 0.3, z: 0.2 },
                panelId: "panel-todo"
            },
            gemini: {
                name: "GEMINI (Agents)",
                function: "AI Agent Operations",
                color: "#FF8C00", // Orange
                position: { x: 0.8, y: 0.3, z: 0.2 },
                panelId: "panel-agents"
            },
            qwen: {
                name: "QWEN (Workers)",
                function: "Worker Grid & Service Mesh",
                color: "#20B2AA", // Teal
                position: { x: -0.8, y: -0.3, z: 0.2 },
                panelId: "panel-workers"
            },
            llama: {
                name: "LLAMA (Database)",
                function: "CRM/Database Health",
                color: "#87CEEB", // Sky Blue
                position: { x: 0.8, y: -0.3, z: 0.2 },
                panelId: "panel-db"
            },
            echo: {
                name: "ECHO (Logs)",
                function: "System Logs & Diagnostics",
                color: "#FF4500", // Red
                position: { x: 0, y: 0.6, z: -0.3 },
                panelId: "panel-logs"
            },
            azr: {
                name: "AZR (LLM Core)",
                function: "Brain Center/LLM Orchestration",
                color: "#4B0082", // Indigo
                position: { x: 0, y: 0, z: 0 },
                panelId: "panel-core"
            }
        };

        // Gemini API Configuration for Structured Output
        const GEMINI_CONFIG = {
            apiKey: localStorage.getItem('gemini_api_key') || '',
            baseUrl: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent',
            schemas: {
                agentAction: {
                    type: "ARRAY",
                    items: {
                        type: "OBJECT",
                        properties: {
                            actionName: { type: "STRING" },
                            agentTarget: { type: "STRING" },
                            priority: { type: "STRING", enum: ["low", "medium", "high"] },
                            createdAt: { type: "STRING", format: "date-time" }
                        },
                        required: ["actionName", "agentTarget", "priority", "createdAt"],
                        propertyOrdering: ["actionName", "agentTarget", "priority", "createdAt"]
                    }
                },
                hemisphereEvent: {
                    type: "OBJECT",
                    properties: {
                        hemisphere: { type: "STRING", enum: ["phi3", "gemini", "qwen", "llama", "echo", "azr"] },
                        eventType: { type: "STRING", enum: ["pulse", "activity", "status_update", "error"] },
                        message: { type: "STRING" },
                        timestamp: { type: "STRING", format: "date-time" },
                        color: { type: "STRING" }
                    },
                    required: ["hemisphere", "eventType", "message", "timestamp"],
                    propertyOrdering: ["hemisphere", "eventType", "message", "timestamp", "color"]
                }
            }
        };

        // ===== STRUCTURED OUTPUT SYSTEM =====
        // Core function for Gemini API calls with structured schemas
        const callGeminiWithSchema = async (prompt, schema, responseMimeType = "application/json") => {
            if (!GEMINI_CONFIG.apiKey) {
                throw new Error('Gemini API key not configured');
            }

            const body = {
                contents: [{ parts: [{ text: prompt }] }],
                generationConfig: {
                    responseMimeType,
                    responseSchema: schema
                }
            };

            const response = await fetch(`${GEMINI_CONFIG.baseUrl}?key=${GEMINI_CONFIG.apiKey}`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(body)
            });

            const json = await response.json();
            try {
                return JSON.parse(json.candidates?.[0]?.content?.parts?.[0]?.text || "[]");
            } catch (err) {
                console.warn('Failed to parse Gemini response:', err);
                return [];
            }
        };

        // Hemisphere pulse and activity system
        const flashHemisphere = (hemisphereId, message, duration = 2000) => {
            const hemisphere = BRAIN_HEMISPHERES[hemisphereId];
            if (!hemisphere) return;

            // Create visual pulse notification
            const flashEl = document.createElement('div');
            flashEl.style.cssText = `
                position: fixed;
                bottom: 40px;
                left: 50%;
                transform: translateX(-50%);
                background: ${hemisphere.color};
                border-radius: 20px;
                padding: 10px 26px;
                font-weight: bold;
                font-size: 1rem;
                box-shadow: 0 0 40px 12px ${hemisphere.color}77;
                color: #fff;
                z-index: 99999;
                transition: opacity 0.9s;
            `;
            flashEl.textContent = message || `${hemisphere.name} Activity`;
            document.body.appendChild(flashEl);

            setTimeout(() => flashEl.style.opacity = 0, duration - 500);
            setTimeout(() => flashEl.remove(), duration);

            // Update activity ticker
            updateActivityTicker(`${hemisphere.name}: ${message}`);

            // Trigger 3D brain hemisphere pulse (if 3D scene exists)
            if (window.brainScene && window.brainScene.pulseHemisphere) {
                window.brainScene.pulseHemisphere(hemisphereId, hemisphere.color);
            }
        };

        // Activity ticker system
        const createActivityTicker = () => {
            const ticker = document.createElement('div');
            ticker.id = 'activity-ticker';
            ticker.style.cssText = `
                position: fixed;
                left: 0;
                right: 0;
                bottom: 0;
                background: #1a083d;
                color: #b3a1ff;
                font-family: monospace;
                font-size: 0.95em;
                padding: 7px 20px 7px 45px;
                letter-spacing: 0.1em;
                z-index: 9999;
                box-shadow: 0 -2px 24px 0 #4B008233;
                display: flex;
                align-items: center;
            `;
            ticker.innerHTML = `<span style="font-size:1.3em; margin-right:10px;">🧠</span> <span id="ticker-message">Agent Lee Cognitive Core Initializing...</span>`;
            document.body.appendChild(ticker);
            return ticker;
        };

        const updateActivityTicker = (message) => {
            const tickerMsg = document.getElementById('ticker-message');
            if (tickerMsg) {
                tickerMsg.textContent = message;
            }
        };

        // Speech Recognition Setup
        const initSpeechRecognition = () => {
            if (!window.webkitSpeechRecognition && !window.SpeechRecognition) {
                console.warn('Speech Recognition not supported');
                return null;
            }

            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();
            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.lang = 'en-US';

            return recognition;
        };

        // ===== UNIFIED PANEL NAVIGATION SYSTEM =====
        // Panel management for unified dashboard
        const showPanel = (panelId) => {
            // Hide all panels
            document.querySelectorAll('[id^="panel-"]').forEach(panel => {
                panel.classList.add('hidden');
            });

            // Show selected panel
            const targetPanel = document.getElementById(panelId);
            if (targetPanel) {
                targetPanel.classList.remove('hidden');

                // Update navigation state
                document.querySelectorAll('.nav-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                const activeBtn = document.querySelector(`[onclick="showPanel('${panelId}')"]`);
                if (activeBtn) {
                    activeBtn.classList.add('active');
                }

                // Flash corresponding hemisphere
                const hemisphereId = Object.keys(BRAIN_HEMISPHERES).find(key =>
                    BRAIN_HEMISPHERES[key].panelId === panelId
                );

                if (hemisphereId) {
                    const hemisphere = BRAIN_HEMISPHERES[hemisphereId];
                    flashHemisphere(hemisphereId, `Accessing ${hemisphere.function}`);
                }

                // Initialize panel-specific functionality
                initializePanelContent(panelId);
            }
        };

        // Initialize content for specific panels
        const initializePanelContent = (panelId) => {
            switch (panelId) {
                case 'panel-core':
                    initializeCognitiveCore();
                    break;
                case 'panel-todo':
                    initializeTodoSystem();
                    break;
                case 'panel-agents':
                    initializeAgentCenter();
                    break;
                case 'panel-workers':
                    initializeWorkersCenter();
                    break;
                case 'panel-db':
                    initializeDatabaseCenter();
                    break;
                case 'panel-logs':
                    initializeLogsCenter();
                    break;
            }
        };

        // ===== UNIFIED DASHBOARD FUNCTIONS =====

        // Toggle between React app and unified dashboard
        const toggleUnifiedDashboard = () => {
            const reactRoot = document.getElementById('root');
            const unifiedNav = document.getElementById('unified-nav');
            const unifiedDash = document.getElementById('unified-dashboard');

            if (unifiedNav.classList.contains('hidden')) {
                // Show unified dashboard
                reactRoot.style.display = 'none';
                unifiedNav.classList.remove('hidden');
                unifiedDash.classList.remove('hidden');

                // Initialize activity ticker
                createActivityTicker();
                updateActivityTicker('Unified Dashboard: All systems online');

                // Flash AZR hemisphere
                flashHemisphere('azr', 'Unified Dashboard Activated');

                // Initialize default panel
                showPanel('panel-core');
            } else {
                // Show React app
                reactRoot.style.display = 'block';
                unifiedNav.classList.add('hidden');
                unifiedDash.classList.add('hidden');

                updateActivityTicker('React Interface: Agent Lee card active');
            }
        };

        // Test structured output with Gemini
        const testStructuredOutput = async () => {
            try {
                updateActivityTicker('Testing Gemini structured output...');
                flashHemisphere('azr', 'Testing Structured Output');

                const prompt = "Agent Lee is running its cognitive core. List the next 3 actions in JSON: actionName, agentTarget, priority, createdAt (ISO8601).";
                const actions = await callGeminiWithSchema(prompt, GEMINI_CONFIG.schemas.agentAction);

                console.log('🧠 Structured Output Test Result:', actions);
                updateActivityTicker(`Structured Output: ${actions.length} actions generated`);
                flashHemisphere('gemini', 'Structured Output Success');

                // Display results in console and update UI
                if (actions.length > 0) {
                    alert(`✅ Structured Output Test Successful!\n\nGenerated ${actions.length} actions:\n${actions.map(a => `• ${a.actionName} (${a.priority})`).join('\n')}`);
                }
            } catch (error) {
                console.error('❌ Structured Output Test Failed:', error);
                updateActivityTicker('Structured Output: Test failed');
                flashHemisphere('echo', 'Test Error');
                alert('❌ Structured Output Test Failed. Check console for details.');
            }
        };

        // Pulse all hemispheres
        const pulseAllHemispheres = () => {
            updateActivityTicker('Pulsing all brain hemispheres...');

            Object.keys(BRAIN_HEMISPHERES).forEach((hemisphereId, index) => {
                setTimeout(() => {
                    const hemisphere = BRAIN_HEMISPHERES[hemisphereId];
                    flashHemisphere(hemisphereId, `${hemisphere.name} Pulse`);
                }, index * 500);
            });

            setTimeout(() => {
                updateActivityTicker('All hemispheres: Pulse sequence complete');
            }, Object.keys(BRAIN_HEMISPHERES).length * 500);
        };

        // Initialize all systems
        const initializeAllSystems = () => {
            updateActivityTicker('Initializing all Agent Lee systems...');
            flashHemisphere('azr', 'System Initialization');

            // Initialize each panel
            Object.keys(BRAIN_HEMISPHERES).forEach((hemisphereId, index) => {
                setTimeout(() => {
                    const hemisphere = BRAIN_HEMISPHERES[hemisphereId];
                    initializePanelContent(hemisphere.panelId);
                    flashHemisphere(hemisphereId, `${hemisphere.name} Initialized`);
                }, index * 1000);
            });

            setTimeout(() => {
                updateActivityTicker('All systems: Initialization complete');
                flashHemisphere('azr', 'All Systems Online');
            }, Object.keys(BRAIN_HEMISPHERES).length * 1000);
        };

        // Exact Google Search URL from user
        const GOOGLE_SEARCH_URL = "https://www.google.com/search?q=&sca_esv=57b167c83bb1e9fa&sxsrf=AE3TifPQ0YGgc5JiKcIqRgnTEnFQbLaGPw%3A1749818549574&source=hp&ei=tRxMaKz-IP2q0PEP76PN-Qo&iflsig=AOw8s4IAAAAAaEwqxfA9Ez8x8_JWVEvwR8sAYgWgeyXD&aep=22&udm=50&ved=0ahUKEwjsrMSItu6NAxV9FTQIHe9RM68QteYPCBM";

        // Exact Telegram URL from user
        const TELEGRAM_URL = "https://web.telegram.org/a/";

        // Search with Google function - Updated for exact URL
        const searchWithGoogle = (query = '') => {
            // Use the specific URL provided by user
            let url = GOOGLE_SEARCH_URL;

            // If a query is provided, replace the empty q parameter
            if (query && query.trim()) {
                const encodedQuery = encodeURIComponent(query.trim());
                url = url.replace('q=&', `q=${encodedQuery}&`);
            }

            return {
                title: query ? `Search: ${query}` : 'Natural Language Search',
                url: url,
                width: 1000,
                height: 700
            };
        };

        // Voice recognition and search
        const listenAndSearch = (recognition, onResult, onError) => {
            if (!recognition) {
                onError('Speech recognition not available');
                return;
            }

            recognition.onresult = (event) => {
                const voiceQuery = event.results[0][0].transcript;
                console.log('🎤 Voice Query:', voiceQuery);
                onResult(voiceQuery);
            };

            recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                onError(event.error);
            };

            recognition.start();
        };

        // ===== PANEL INITIALIZATION FUNCTIONS =====

        // Initialize Cognitive Core (3D Brain + LLM Center)
        const initializeCognitiveCore = () => {
            console.log('🧠 Initializing Cognitive Core...');
            updateActivityTicker('Cognitive Core: 3D Brain visualization loading...');

            // Initialize 3D brain if not already done
            if (!window.brainScene) {
                initializeBrainVisualization();
            }

            // Load LLM status
            updateLLMStatus();
        };

        // Initialize Todo System
        const initializeTodoSystem = () => {
            console.log('📋 Initializing Todo System...');
            updateActivityTicker('PHI-3 To-Do: Task management system active');

            // Load existing todos
            loadTodoItems();

            // Initialize task automation
            initializeTaskAutomation();
        };

        // Initialize Agent Center
        const initializeAgentCenter = () => {
            console.log('🤖 Initializing Agent Center...');
            updateActivityTicker('GEMINI Agents: Agent operations center online');

            // Load agent status
            loadAgentStatus();

            // Initialize agent communication
            initializeAgentCommunication();
        };

        // Initialize Workers Center
        const initializeWorkersCenter = () => {
            console.log('⚙️ Initializing Workers Center...');
            updateActivityTicker('QWEN Workers: Service mesh and worker grid active');

            // Load worker status
            loadWorkerStatus();

            // Initialize worker monitoring
            initializeWorkerMonitoring();
        };

        // Initialize Database Center
        const initializeDatabaseCenter = () => {
            console.log('🗄️ Initializing Database Center...');
            updateActivityTicker('LLAMA Database: CRM and data health monitoring active');

            // Load database health
            loadDatabaseHealth();

            // Initialize data monitoring
            initializeDatabaseMonitoring();
        };

        // Initialize Logs Center
        const initializeLogsCenter = () => {
            console.log('📊 Initializing Logs Center...');
            updateActivityTicker('ECHO Logs: System diagnostics and logging active');

            // Load recent logs
            loadSystemLogs();

            // Initialize real-time log streaming
            initializeLogStreaming();
        };

        // ===== STUB FUNCTIONS FOR PANEL INITIALIZATION =====

        // 3D Brain Visualization
        const initializeBrainVisualization = () => {
            console.log('🧠 Initializing 3D Brain Visualization...');
            // This would initialize the Three.js brain scene
            window.brainScene = {
                pulseHemisphere: (hemisphereId, color) => {
                    console.log(`🧠 Brain pulse: ${hemisphereId} with color ${color}`);
                }
            };
        };

        // LLM Status
        const updateLLMStatus = () => {
            console.log('🤖 Updating LLM Status...');
            // Update status of all LLM models
        };

        // Todo Items
        const loadTodoItems = () => {
            console.log('📋 Loading Todo Items...');
            // Load existing todo items from storage
        };

        const initializeTaskAutomation = () => {
            console.log('⚙️ Initializing Task Automation...');
            // Initialize automated task processing
        };

        // Agent Status
        const loadAgentStatus = () => {
            console.log('🤖 Loading Agent Status...');
            // Load current agent statuses
        };

        const initializeAgentCommunication = () => {
            console.log('📡 Initializing Agent Communication...');
            // Initialize inter-agent communication
        };

        // Worker Status
        const loadWorkerStatus = () => {
            console.log('⚙️ Loading Worker Status...');
            // Load current worker statuses
        };

        const initializeWorkerMonitoring = () => {
            console.log('📊 Initializing Worker Monitoring...');
            // Initialize worker health monitoring
        };

        // Database Health
        const loadDatabaseHealth = () => {
            console.log('🗄️ Loading Database Health...');
            // Load database health metrics
        };

        const initializeDatabaseMonitoring = () => {
            console.log('📈 Initializing Database Monitoring...');
            // Initialize database monitoring
        };

        // System Logs
        const loadSystemLogs = () => {
            console.log('📊 Loading System Logs...');
            // Load recent system logs
        };

        const initializeLogStreaming = () => {
            console.log('📡 Initializing Log Streaming...');
            // Initialize real-time log streaming
        };

        // ===== CRITICAL: LLM ROUTING & TASK DELEGATION LOGIC =====

        // Echo System Dispatcher - Core LLM Routing Logic
        class EchoSystemDispatcher {
            constructor() {
                this.llmEndpoints = {
                    azr: 'streamlined', // Streamlined AZR via Wllama
                    phi3: 'streamlined', // Streamlined PHI-3
                    gemini: 'streamlined', // Streamlined Gemma
                    qwen: 'streamlined', // Streamlined Llama
                    llama: 'streamlined', // Streamlined Llama
                    embedder: 'streamlined' // Streamlined Embedder
                };

                this.routingRules = {
                    // AZR Framework Tasks (Complex Reasoning)
                    azr: [
                        'plan', 'strategize', 'verify complex logic', 'multi-step reasoning',
                        'analyze system architecture', 'debug complex issues', 'design patterns'
                    ],

                    // PHI-3 Tasks (Quick Classification/Validation)
                    phi3: [
                        'validate email format', 'quick classification', 'simple yes/no',
                        'format validation', 'basic categorization', 'quick check'
                    ],

                    // Gemini Tasks (UI Echo Generation & Semantic Understanding)
                    gemini: [
                        'ui echo generation', 'semantic search', 'natural language understanding',
                        'content generation', 'creative writing', 'nuanced interpretation'
                    ],

                    // Streamlined LLM Tasks (General Purpose)
                    streamlined: [
                        'summarization', 'chat response', 'general conversation',
                        'text processing', 'content analysis', 'general llm work'
                    ]
                };

                this.currentSession = null;
                this.taskQueue = [];
            }

            // Main routing decision function with confidence scoring
            routeTask(userInput, context = {}) {
                const normalizedInput = userInput.toLowerCase();
                let bestMatch = {
                    target: 'streamlined',
                    method: 'processWithStreamlined',
                    confidence: 0.1,
                    reasoning: 'default fallback'
                };

                // Check for desktop automation keywords first (highest priority)
                if (this.isDesktopAutomationTask(normalizedInput)) {
                    return {
                        target: 'electron',
                        method: 'dispatchIntent',
                        confidence: 0.95,
                        reasoning: 'Desktop automation keywords detected'
                    };
                }

                // Check each LLM with confidence scoring
                for (const [llmType, keywords] of Object.entries(this.routingRules)) {
                    for (const keyword of keywords) {
                        if (normalizedInput.includes(keyword)) {
                            const confidence = this.calculateConfidence(normalizedInput, keyword, llmType, context);
                            if (confidence > bestMatch.confidence) {
                                bestMatch = {
                                    target: llmType,
                                    method: this.getMethodForLLM(llmType),
                                    confidence: confidence,
                                    reasoning: `Matched keyword: "${keyword}" for ${llmType} (confidence: ${confidence.toFixed(2)})`
                                };
                            }
                        }
                    }
                }

                // Context-based routing adjustments
                bestMatch = this.applyContextualAdjustments(bestMatch, normalizedInput, context);

                return bestMatch;
            }

            // Calculate confidence score for routing decisions
            calculateConfidence(input, keyword, llmType, context = {}) {
                let confidence = 0.6; // Base confidence

                // Boost confidence for exact matches
                if (input === keyword) confidence += 0.3;

                // Boost for LLM-specific patterns
                if (llmType === 'azr' && (input.includes('complex') || input.includes('analyze') || input.includes('reason'))) {
                    confidence += 0.2;
                }
                if (llmType === 'phi3' && (input.includes('quick') || input.includes('validate') || input.includes('check'))) {
                    confidence += 0.2;
                }
                if (llmType === 'gemini' && (input.includes('generate') || input.includes('create') || input.includes('ui'))) {
                    confidence += 0.2;
                }
                if (llmType === 'webllm' && (input.includes('summarize') || input.includes('explain') || input.includes('chat'))) {
                    confidence += 0.1;
                }

                // Length-based adjustments
                if (input.length > 100 && llmType === 'azr') confidence += 0.15; // Long complex tasks
                if (input.length < 30 && llmType === 'phi3') confidence += 0.15; // Short quick tasks
                if (input.length > 50 && input.length < 150 && llmType === 'gemini') confidence += 0.1; // Medium creative tasks

                // Context-based adjustments
                if (context.source === 'voice_input' && llmType === 'phi3') confidence += 0.1; // Voice often needs quick processing
                if (context.sessionId && llmType === 'webllm') confidence += 0.05; // Ongoing conversations

                // Multiple keyword matches boost
                const keywordMatches = this.routingRules[llmType].filter(kw => input.includes(kw)).length;
                if (keywordMatches > 1) confidence += (keywordMatches - 1) * 0.1;

                return Math.min(confidence, 0.95); // Cap at 95%
            }

            // Apply contextual adjustments to routing decisions
            applyContextualAdjustments(routing, input, context) {
                // If previous task was complex and this seems related, prefer AZR
                if (context.previousLLM === 'azr' && input.includes('continue')) {
                    routing.confidence += 0.1;
                    routing.reasoning += ' (contextual continuity)';
                }

                // If user seems frustrated (multiple attempts), prefer more capable LLM
                if (context.attemptCount > 2) {
                    if (routing.target === 'phi3' || routing.target === 'webllm') {
                        routing.target = 'azr';
                        routing.method = 'processWithAZR';
                        routing.confidence = Math.min(routing.confidence + 0.2, 0.9);
                        routing.reasoning += ' (escalated due to multiple attempts)';
                    }
                }

                // Time-based adjustments
                const hour = new Date().getHours();
                if (hour < 9 || hour > 18) { // Outside work hours, prefer faster responses
                    if (routing.target === 'azr' && routing.confidence < 0.8) {
                        routing.target = 'gemini';
                        routing.method = 'processWithGemini';
                        routing.reasoning += ' (off-hours preference for speed)';
                    }
                }

                return routing;
            }

            // Check if task requires desktop automation
            isDesktopAutomationTask(input) {
                const automationKeywords = [
                    'click', 'type', 'press key', 'move mouse', 'screenshot',
                    'open application', 'close window', 'automate', 'control desktop'
                ];

                return automationKeywords.some(keyword => input.includes(keyword));
            }

            // Get processing method for each LLM type
            getMethodForLLM(llmType) {
                const methods = {
                    azr: 'processWithAZR',
                    phi3: 'processWithPHI3',
                    gemini: 'processWithGemini',
                    qwen: 'processWithQwen',
                    webllm: 'processWithWebLLM',
                    streamlined: 'processWithStreamlined'
                };

                return methods[llmType] || 'processWithStreamlined';
            }

            // Execute routed task
            async executeTask(userInput, context = {}) {
                const routing = this.routeTask(userInput, context);

                console.log('🧠 LLM Routing Decision:', routing);

                // Log the routing decision to telemetry
                if (window.AgentLeeDBHelpers) {
                    await window.AgentLeeDBHelpers.addRecord('telemetry', {
                        entry_id: `routing_${Date.now()}`,
                        region: 'Echo System',
                        event_type: 'llm_routing',
                        user_command: userInput,
                        routing_decision: routing,
                        timestamp: new Date().toISOString()
                    });
                }

                // Execute based on routing decision
                switch (routing.target) {
                    case 'electron':
                        return await this.executeDesktopAutomation(userInput);
                    case 'azr':
                        return await this.processWithAZR(userInput, context);
                    case 'phi3':
                        return await this.processWithPHI3(userInput, context);
                    case 'gemini':
                        return await this.processWithGemini(userInput, context);
                    case 'qwen':
                        return await this.processWithQwen(userInput, context);
                    case 'webllm':
                        return await this.processWithWebLLM(userInput, context);
                    case 'streamlined':
                    default:
                        return await this.processWithStreamlined(userInput, context);
                }
            }

            // Desktop automation execution
            async executeDesktopAutomation(command) {
                if (window.electronAPI && window.electronAPI.dispatchIntent) {
                    try {
                        const result = await window.electronAPI.dispatchIntent(command);
                        return {
                            success: true,
                            result: result,
                            source: 'electron_automation'
                        };
                    } catch (error) {
                        return {
                            success: false,
                            error: error.message,
                            source: 'electron_automation'
                        };
                    }
                } else {
                    return {
                        success: false,
                        error: 'Electron API not available',
                        source: 'electron_automation'
                    };
                }
            }

            // AZR processing via Streamlined LLM Manager
            async processWithAZR(input, context) {
                try {
                    if (!window.StreamlinedLLMManager) {
                        throw new Error('Streamlined LLM Manager not available');
                    }

                    // Use the streamlined reasoning system
                    const azrResponse = await window.StreamlinedLLMManager.reason(input, context);

                    return {
                        success: true,
                        result: azrResponse.solution || azrResponse.text || 'AZR processing complete',
                        source: 'azr_streamlined',
                        metadata: {
                            model: 'andrewzh_Absolute_Zero_Reasoner-Coder-3b',
                            steps: azrResponse.reasoning_steps || [],
                            confidence: azrResponse.confidence || null,
                            hemisphere: 'azr'
                        }
                    };

                } catch (error) {
                    console.warn('AZR unavailable, falling back to chat:', error);

                    // Fallback to regular chat
                    if (window.StreamlinedLLMManager) {
                        const chatResult = await window.StreamlinedLLMManager.chat(input);
                        return {
                            success: true,
                            result: chatResult.text || 'Processing complete',
                            source: 'chat_fallback'
                        };
                    }

                    return {
                        success: false,
                        result: 'LLM system not available',
                        source: 'fallback'
                    };
                }
            }

            // PHI-3 local processing
            async processWithPHI3(input, context) {
                try {
                    if (!window.FrontendLLMManager) {
                        throw new Error('Frontend LLM Manager not available');
                    }

                    // Initialize PHI-3 if not already loaded
                    if (!window.FrontendLLMManager.llmInstances.has('phi3')) {
                        console.log('🔄 Loading PHI-3 model...');
                        await window.FrontendLLMManager.initializeLLM('phi3');
                    }

                    const phi3Instance = window.FrontendLLMManager.llmInstances.get('phi3');
                    const result = await phi3Instance.generate(input, {
                        maxTokens: 150,
                        temperature: 0.3, // Lower temperature for validation tasks
                        ...context
                    });

                    return {
                        success: true,
                        result: result,
                        source: 'phi3_local'
                    };
                } catch (error) {
                    console.warn('PHI-3 unavailable, falling back to WebLLM:', error);
                    return await this.processWithWebLLM(input, context);
                }
            }

            // Gemini API processing
            async processWithGemini(input, context) {
                try {
                    if (!window.FrontendLLMManager) {
                        throw new Error('Frontend LLM Manager not available');
                    }

                    // For Echo generation, use specialized prompt
                    let prompt = input;
                    if (context.echoGeneration) {
                        prompt = `Generate a dynamic UI echo for the following context: ${input}.
                        Create an engaging, contextually appropriate response that guides the user's next action.
                        Focus on being helpful, intuitive, and encouraging.`;
                    }

                    // Initialize Gemini if not already loaded
                    if (!window.FrontendLLMManager.llmInstances.has('gemini')) {
                        console.log('🔄 Loading Gemini model...');
                        await window.FrontendLLMManager.initializeLLM('gemini');
                    }

                    const geminiInstance = window.FrontendLLMManager.llmInstances.get('gemini');
                    const result = await geminiInstance.generate(prompt, {
                        temperature: 0.8, // Higher temperature for creative echo generation
                        maxTokens: 200,
                        ...context
                    });

                    return {
                        success: true,
                        result: result,
                        source: 'gemini_api'
                    };
                } catch (error) {
                    console.warn('Gemini API unavailable, falling back to WebLLM:', error);
                    return await this.processWithWebLLM(input, context);
                }
            }

            // Qwen via Transformers.js
            async processWithQwen(input, context) {
                try {
                    if (!window.FrontendLLMManager) {
                        throw new Error('Frontend LLM Manager not available');
                    }

                    // Initialize Qwen if not already loaded
                    if (!window.FrontendLLMManager.llmInstances.has('qwen')) {
                        console.log('🔄 Loading Qwen model...');
                        await window.FrontendLLMManager.initializeLLM('qwen');
                    }

                    const qwenInstance = window.FrontendLLMManager.llmInstances.get('qwen');
                    const result = await qwenInstance.generate(input, {
                        maxTokens: 120,
                        temperature: 0.6,
                        ...context
                    });

                    return {
                        success: true,
                        result: result,
                        source: 'qwen_transformers'
                    };
                } catch (error) {
                    console.warn('Qwen unavailable, falling back to WebLLM:', error);
                    return await this.processWithWebLLM(input, context);
                }
            }

            // Streamlined LLM processing (primary method)
            async processWithStreamlined(input, context) {
                try {
                    if (!window.StreamlinedLLMManager) {
                        throw new Error('Streamlined LLM Manager not available');
                    }

                    // Use appropriate method based on input type
                    let result;
                    if (input.toLowerCase().includes('reason') || input.toLowerCase().includes('solve')) {
                        result = await window.StreamlinedLLMManager.reason(input, context);
                        return {
                            success: true,
                            result: result.solution || result.text,
                            source: 'streamlined_reasoning'
                        };
                    } else {
                        result = await window.StreamlinedLLMManager.chat(input, context.conversationHistory || []);
                        return {
                            success: true,
                            result: result.text,
                            source: 'streamlined_chat'
                        };
                    }
                } catch (error) {
                    console.warn('Streamlined LLM processing failed, using intelligent fallback');

                    // Intelligent fallback response based on input
                    let fallbackResponse;
                    const inputLower = input.toLowerCase();

                    if (inputLower.includes('hello') || inputLower.includes('hi')) {
                        fallbackResponse = "Hello! I'm Agent Lee running in offline mode. How can I assist you today?";
                    } else if (inputLower.includes('help')) {
                        fallbackResponse = "I'm here to help! I'm currently running with limited capabilities in offline mode. I can still assist with basic tasks and information.";
                    } else if (inputLower.includes('status')) {
                        fallbackResponse = "System Status: Agent Lee is operational in offline mode. Core functions are available, but advanced AI processing is limited.";
                    } else {
                        fallbackResponse = `I understand you're asking about: "${input.substring(0, 50)}...". I'm currently running in offline mode with limited AI capabilities. The system is functional but some advanced features may not be available.`;
                    }

                    return {
                        success: true,
                        result: fallbackResponse,
                        source: 'fallback',
                        metadata: {
                            model: 'fallback',
                            timestamp: new Date().toISOString(),
                            note: 'Running in offline mode'
                        }
                    };
                }
            }

            // WebLLM compatibility method (redirects to streamlined)
            async processWithWebLLM(input, context) {
                console.log('⚠️ processWithWebLLM is deprecated - redirecting to StreamlinedLLMManager');
                return await this.processWithStreamlined(input, context);
            }
        }

        // Note: EchoDispatcher will be initialized after all dependencies are loaded

        // ===== STREAMLINED LLM INTEGRATION (NO WEBSOCKET NEEDED) =====

        // Note: AZR Bridge WebSocket Client has been replaced by Streamlined LLM Manager
        // All LLM functionality now runs directly in the browser via Wllama and Transformers.js

        // Placeholder class to prevent errors from old references
        class AZRBridgeClientPlaceholder {
            constructor() {
                this.isConnected = false;
                console.log('⚠️ AZR Bridge Client is deprecated - use Streamlined LLM Manager');
            }

            async connect() {
                console.log('⚠️ AZR Bridge is deprecated - use StreamlinedLLMManager.reason() instead');
                return Promise.resolve();
            }

            async sendTask(taskData) {
                console.log('⚠️ AZR Bridge is deprecated - use StreamlinedLLMManager.reason() instead');
                return Promise.resolve({ result: 'Use StreamlinedLLMManager instead' });
            }

            getConnectionStatus() {
                return { connected: false, deprecated: true };
            }


        }

        // Initialize placeholder AZR Bridge Client (deprecated)
        window.AZRBridgeClient = new AZRBridgeClientPlaceholder();

        // Test function for AZR connection
        window.testAZRConnection = async function() {
            try {
                console.log('🧪 Testing AZR connection...');

                if (!window.AZRBridgeClient.isConnected) {
                    await window.AZRBridgeClient.connect();
                }

                const response = await window.AZRBridgeClient.sendTask({
                    type: 'azr_task',
                    prompt: 'Hello AZR, please respond with a simple greeting.',
                    request_id: `test_${Date.now()}`
                });

                console.log('✅ AZR Test Response:', response);
                return response;

            } catch (error) {
                console.error('❌ AZR Test Failed:', error);
                return { error: error.message };
            }
        };

        // Comprehensive intelligence test
        window.testAgentIntelligence = async function() {
            try {
                console.log('🧠 Testing Agent Lee Intelligence...');

                const testPrompt = "Analyze this scenario: A user wants to organize their digital files efficiently. What would be the best approach considering both technical and psychological factors?";

                const result = await window.EchoDispatcher.executeTask(testPrompt, {
                    source: 'intelligence_test',
                    timestamp: new Date().toISOString()
                });

                console.log('🎯 Intelligence Test Result:', result);

                // Display result in UI
                const responseDiv = document.getElementById('agentResponse');
                if (responseDiv) {
                    responseDiv.innerHTML = `
                        <h4>🧠 Intelligence Test Result:</h4>
                        <p><strong>Source:</strong> ${result.source}</p>
                        <p><strong>Response:</strong> ${result.result}</p>
                        <p><strong>Success:</strong> ${result.success ? '✅' : '❌'}</p>
                    `;
                }

                return result;

            } catch (error) {
                console.error('❌ Intelligence Test Failed:', error);
                return { error: error.message };
            }
        };

        // Process user input function
        window.processUserInput = async function() {
            const inputElement = document.getElementById('userInput');
            const responseDiv = document.getElementById('agentResponse');

            if (!inputElement || !responseDiv) {
                console.error('UI elements not found');
                return;
            }

            const userInput = inputElement.value.trim();
            if (!userInput) return;

            // Clear input
            inputElement.value = '';

            // Show processing
            responseDiv.innerHTML = '<p>🤔 Agent Lee is thinking...</p>';

            try {
                const result = await window.EchoDispatcher.executeTask(userInput, {
                    source: 'user_input',
                    timestamp: new Date().toISOString()
                });

                responseDiv.innerHTML = `
                    <div class="response-container">
                        <h4>🧠 Agent Lee Response:</h4>
                        <p>${result.result}</p>
                        <small>Source: ${result.source} | ${result.success ? '✅ Success' : '❌ Error'}</small>
                    </div>
                `;

            } catch (error) {
                responseDiv.innerHTML = `
                    <div class="error-container">
                        <h4>❌ Error:</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        };

        // Show system status
        window.showSystemStatus = function() {
            const azrStatus = window.AZRBridgeClient ? window.AZRBridgeClient.getConnectionStatus() : { connected: false };
            const llmStatus = window.FrontendLLMManager ? window.FrontendLLMManager.getLLMStatus() : {};

            console.log('🔍 System Status:', {
                azrBridge: azrStatus,
                frontendLLMs: llmStatus,
                echoDispatcher: !!window.EchoDispatcher,
                agentMemory: !!window.AgentLeeDBHelpers
            });

            const responseDiv = document.getElementById('agentResponse');
            if (responseDiv) {
                responseDiv.innerHTML = `
                    <div class="status-container">
                        <h4>📊 System Status</h4>
                        <p><strong>AZR Bridge:</strong> ${azrStatus.connected ? '✅ Connected' : '❌ Disconnected'}</p>
                        <p><strong>Echo Dispatcher:</strong> ${window.EchoDispatcher ? '✅ Active' : '❌ Inactive'}</p>
                        <p><strong>Frontend LLMs:</strong> ${Object.keys(llmStatus).length} configured</p>
                        <p><strong>Memory System:</strong> ${window.AgentLeeDBHelpers ? '✅ Active' : '❌ Inactive'}</p>
                    </div>
                `;
            }
        };

        // ===== CRITICAL: ECHO SYSTEM WITH FRONTEND LLM INTEGRATION =====

        // Frontend LLM Manager for Echo System
        class FrontendLLMManager {
            constructor() {
                this.llmInstances = new Map();
                this.loadingStatus = new Map();
                this.initializationPromises = new Map();

                // LLM configurations based on the file requirements
                this.llmConfigs = {
                    phi3: {
                        type: 'transformers',
                        modelPath: 'microsoft/Phi-3-mini-4k-instruct',
                        localPath: './llama_models/fine_tuned_phi3/',
                        purpose: 'Quick validation and classification',
                        priority: 1
                    },
                    gemini: {
                        type: 'gemini_sdk',
                        apiKey: null, // To be set by user
                        purpose: 'UI Echo generation and semantic analysis',
                        priority: 2
                    },
                    qwen: {
                        type: 'transformers',
                        modelPath: 'Qwen/Qwen2-0.5B-Instruct',
                        purpose: 'Lightweight reasoning and verification',
                        priority: 3
                    },
                    webllm: {
                        type: 'webllm',
                        modelPath: 'Llama-3.2-1B-Instruct-q4f32_1-MLC',
                        purpose: 'General purpose frontend LLM',
                        priority: 4
                    }
                };

                console.log('🧠 Frontend LLM Manager initialized');
            }

            async initializeLLM(llmType) {
                if (this.initializationPromises.has(llmType)) {
                    return this.initializationPromises.get(llmType);
                }

                const promise = this._doInitializeLLM(llmType);
                this.initializationPromises.set(llmType, promise);
                return promise;
            }

            async _doInitializeLLM(llmType) {
                const config = this.llmConfigs[llmType];
                if (!config) {
                    throw new Error(`Unknown LLM type: ${llmType}`);
                }

                console.log(`🔄 Initializing ${llmType} LLM...`);
                this.loadingStatus.set(llmType, 'loading');

                try {
                    let instance;

                    switch (config.type) {
                        case 'transformers':
                            instance = await this.initializeTransformersLLM(llmType, config);
                            break;
                        case 'gemini_sdk':
                            instance = await this.initializeGeminiLLM(config);
                            break;
                        case 'webllm':
                            instance = await this.initializeWebLLM(config);
                            break;
                        default:
                            throw new Error(`Unsupported LLM type: ${config.type}`);
                    }

                    this.llmInstances.set(llmType, instance);
                    this.loadingStatus.set(llmType, 'ready');

                    console.log(`✅ ${llmType} LLM initialized successfully`);

                    // Broadcast LLM ready event
                    if (window.AgentLeeEventBus) {
                        window.AgentLeeEventBus.postMessage({
                            type: 'LLMInitialized',
                            data: { llmType, status: 'ready', timestamp: new Date().toISOString() }
                        });
                    }

                    return instance;

                } catch (error) {
                    console.error(`❌ Failed to initialize ${llmType}:`, error);
                    this.loadingStatus.set(llmType, 'error');

                    // Broadcast LLM error event
                    if (window.AgentLeeEventBus) {
                        window.AgentLeeEventBus.postMessage({
                            type: 'LLMError',
                            data: { llmType, error: error.message, timestamp: new Date().toISOString() }
                        });
                    }

                    throw error;
                }
            }

            async initializeTransformersLLM(llmType, config) {
                // Check if Transformers.js is available
                if (typeof window.transformers === 'undefined') {
                    // Try to load Transformers.js dynamically
                    if (window.loadTransformers) {
                        const loaded = await window.loadTransformers();
                        if (!loaded) {
                            throw new Error('Transformers.js failed to load');
                        }
                    } else {
                        throw new Error('Transformers.js loader not available');
                    }
                }

                // For PHI-3, try to load from local fine-tuned model first
                if (llmType === 'phi3') {
                    try {
                        // Attempt to load local fine-tuned PHI-3
                        console.log('🔄 Attempting to load local fine-tuned PHI-3...');

                        // Check if local model files exist
                        const localModelPath = './llama_models/fine_tuned_phi3/';

                        // For now, use HuggingFace model as primary option
                        if (window.transformers && window.transformers.pipeline) {
                            const instance = await window.transformers.pipeline('text-generation', config.modelPath, {
                                quantized: true,
                                progress_callback: (progress) => {
                                    console.log(`PHI-3 loading progress: ${Math.round(progress.progress * 100)}%`);
                                }
                            });

                            return {
                                type: 'transformers',
                                model: instance,
                                generate: async (prompt, options = {}) => {
                                    const result = await instance(prompt, {
                                        max_new_tokens: options.maxTokens || 100,
                                        temperature: options.temperature || 0.7,
                                        do_sample: true,
                                        ...options
                                    });
                                    return result[0].generated_text;
                                }
                            };
                        } else {
                            throw new Error('Transformers.js pipeline not available');
                        }

                    } catch (error) {
                        console.warn('Local PHI-3 not available, using fallback:', error);
                        throw error;
                    }
                } else {
                    // Load other Transformers.js models
                    if (window.transformers && window.transformers.pipeline) {
                        const instance = await window.transformers.pipeline('text-generation', config.modelPath, {
                            quantized: true
                        });

                        return {
                            type: 'transformers',
                            model: instance,
                            generate: async (prompt, options = {}) => {
                                const result = await instance(prompt, {
                                    max_new_tokens: options.maxTokens || 100,
                                    temperature: options.temperature || 0.7,
                                    ...options
                                });
                                return result[0].generated_text;
                            }
                        };
                    } else {
                        throw new Error('Transformers.js pipeline not available');
                    }
                }
            }

            async initializeGeminiLLM(config) {
                // Check if Gemini SDK is available
                if (typeof window.google === 'undefined' || !window.google.generativeai) {
                    // Try to load Gemini SDK if API key is available
                    if (config.apiKey && window.loadGeminiSDK) {
                        const loaded = window.loadGeminiSDK(config.apiKey);
                        if (!loaded) {
                            throw new Error('Gemini SDK failed to load');
                        }
                        // Wait a bit for SDK to initialize
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    } else {
                        throw new Error('Gemini SDK not loaded. Please provide API key and include the Gemini SDK script.');
                    }
                }

                if (!config.apiKey) {
                    throw new Error('Gemini API key not configured');
                }

                // Create a mock Gemini interface for now
                return {
                    type: 'gemini',
                    model: null,
                    generate: async (prompt, options = {}) => {
                        // Fallback to a simple response until Gemini SDK is properly loaded
                        return `Gemini response to: ${prompt.substring(0, 50)}...`;
                    }
                };
            }

            async initializeWebLLM(config) {
                // Check if WebLLM is available
                if (typeof window.webllm === 'undefined') {
                    // Wait for WebLLM to load if it's still loading
                    if (window.webllmLoaded === false) {
                        throw new Error('WebLLM failed to load');
                    } else if (window.webllmLoaded === undefined) {
                        // Wait for loading to complete
                        await new Promise((resolve, reject) => {
                            const checkInterval = setInterval(() => {
                                if (window.webllmLoaded === true) {
                                    clearInterval(checkInterval);
                                    resolve();
                                } else if (window.webllmLoaded === false) {
                                    clearInterval(checkInterval);
                                    reject(new Error('WebLLM failed to load'));
                                }
                            }, 100);

                            // Timeout after 10 seconds
                            setTimeout(() => {
                                clearInterval(checkInterval);
                                reject(new Error('WebLLM loading timeout'));
                            }, 10000);
                        });
                    } else {
                        throw new Error('WebLLM not loaded. Please include the WebLLM script.');
                    }
                }

                // Create a mock WebLLM interface for now
                return {
                    type: 'webllm',
                    model: null,
                    generate: async (prompt, options = {}) => {
                        // Fallback to a simple response until WebLLM is properly loaded
                        return `WebLLM response to: ${prompt.substring(0, 50)}...`;
                    }
                };
            }

            async loadTransformersJS() {
                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/@xenova/transformers@2.17.2';
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            }

            async generateEcho(prompt, context = {}, preferredLLM = 'gemini') {
                try {
                    // Try preferred LLM first
                    if (this.llmInstances.has(preferredLLM)) {
                        const instance = this.llmInstances.get(preferredLLM);
                        return await instance.generate(prompt, context);
                    }

                    // Initialize preferred LLM if not available
                    if (this.llmConfigs[preferredLLM]) {
                        const instance = await this.initializeLLM(preferredLLM);
                        return await instance.generate(prompt, context);
                    }

                    // Fallback to any available LLM
                    for (const [llmType, instance] of this.llmInstances) {
                        try {
                            return await instance.generate(prompt, context);
                        } catch (error) {
                            console.warn(`Failed to generate with ${llmType}:`, error);
                        }
                    }

                    throw new Error('No LLM available for echo generation');

                } catch (error) {
                    console.error('Echo generation failed:', error);
                    throw error;
                }
            }

            getLLMStatus() {
                const status = {};
                for (const [llmType, config] of Object.entries(this.llmConfigs)) {
                    status[llmType] = {
                        configured: true,
                        loaded: this.llmInstances.has(llmType),
                        loading: this.loadingStatus.get(llmType) === 'loading',
                        error: this.loadingStatus.get(llmType) === 'error',
                        purpose: config.purpose,
                        priority: config.priority
                    };
                }
                return status;
            }
        }

        // Initialize global Frontend LLM Manager
        window.FrontendLLMManager = new FrontendLLMManager();

        // Initialize REAL Echo System Dispatcher (after all dependencies are loaded)
        if (window.WorkingEchoDispatcher) {
            window.EchoDispatcher = window.WorkingEchoDispatcher;
            console.log('✅ REAL Echo System Dispatcher initialized with all dependencies');
        } else {
            // Fallback to original if real one not available
            window.EchoDispatcher = new EchoSystemDispatcher();
            console.log('⚠️ Using fallback Echo System Dispatcher');
        }

        // ===== SERVICE WORKER REGISTRATION =====

        // Register service worker for offline functionality
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('✅ Service Worker registered:', registration.scope);
                    })
                    .catch((error) => {
                        console.warn('⚠️ Service Worker registration failed:', error);
                    });
            });
        }

        // ===== CRITICAL: MICROFRONTEND LOADER & GUI ORCHESTRATION =====

        // MicrofrontendLoader Class for GUI Management
        class MicrofrontendLoader {
            constructor() {
                this.loadedGuis = new Map();
                this.guiConfigs = {
                    'agents': {
                        url: 'Agent Lee\'s Agent Center.html',
                        title: 'Agent Control Center',
                        defaultSize: { width: 800, height: 600 },
                        icon: '🤖'
                    },
                    'db': {
                        url: 'Agent Lee\'s DB.html',
                        title: 'Database Memory Core',
                        defaultSize: { width: 1000, height: 700 },
                        icon: '🗄️'
                    },
                    'todo': {
                        url: 'Agent Lee\'s Dynamic To-Do List.html',
                        title: 'Dynamic To-Do Nexus',
                        defaultSize: { width: 700, height: 800 },
                        icon: '📋'
                    },
                    'workers': {
                        url: 'Agent Lee\'s Integrated Workers Center.html',
                        title: 'Workers Execution Grid',
                        defaultSize: { width: 900, height: 650 },
                        icon: '👷'
                    },
                    'llmcenter': {
                        url: 'Agent zlee\'s LLM Center.html',
                        title: '3D Brain Visualization',
                        defaultSize: { width: 1200, height: 800 },
                        icon: '🧠'
                    },
                    'logs': {
                        url: 'Agent Lee\'s System Logs.html',
                        title: 'System Logs Monitor',
                        defaultSize: { width: 800, height: 500 },
                        icon: '📝'
                    }
                };
                this.zIndexCounter = 10000;
            }

            async loadGui(guiType, options = {}) {
                const config = this.guiConfigs[guiType];
                if (!config) {
                    throw new Error(`Unknown GUI type: ${guiType}`);
                }

                // Check if GUI is already loaded
                if (this.loadedGuis.has(guiType)) {
                    const existing = this.loadedGuis.get(guiType);
                    this.bringToFront(existing.container);
                    return existing.container;
                }

                console.log(`🔄 Loading GUI: ${guiType}`);

                // Create iframe container
                const guiContainer = this.createGuiContainer(guiType, config, options);

                // Load GUI in iframe
                const iframe = guiContainer.querySelector('.gui-iframe');
                iframe.src = config.url;

                // Setup communication
                this.setupGuiCommunication(guiType, iframe);

                // Store reference
                this.loadedGuis.set(guiType, {
                    container: guiContainer,
                    iframe: iframe,
                    config: config,
                    loadTime: Date.now()
                });

                // Save to gui_registry
                await this.saveGuiState(guiType, guiContainer);

                console.log(`✅ GUI loaded: ${guiType}`);
                return guiContainer;
            }

            createGuiContainer(guiType, config, options) {
                const container = document.createElement('div');
                container.className = 'microfrontend-container';
                container.dataset.guiType = guiType;
                container.dataset.guiId = `gui_${guiType}_${Date.now()}`;

                // Apply default size and position
                const size = options.size || config.defaultSize;
                const position = options.position || this.calculatePosition(guiType);

                container.style.cssText = `
                    position: fixed;
                    width: ${size.width}px;
                    height: ${size.height}px;
                    left: ${position.x}px;
                    top: ${position.y}px;
                    background: rgba(15, 23, 42, 0.95);
                    border: 1px solid var(--primary-color);
                    border-radius: 12px;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 0 15px rgba(0, 242, 255, 0.3);
                    z-index: ${this.zIndexCounter++};
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    backdrop-filter: blur(20px);
                `;

                // Create header
                const header = this.createGuiHeader(guiType, config);
                container.appendChild(header);

                // Create iframe
                const iframe = document.createElement('iframe');
                iframe.className = 'gui-iframe';
                iframe.style.cssText = `
                    flex: 1;
                    border: none;
                    width: 100%;
                    height: 100%;
                    background: transparent;
                `;
                container.appendChild(iframe);

                // Make draggable and resizable
                this.makeInteractive(container);

                document.body.appendChild(container);
                return container;
            }

            createGuiHeader(guiType, config) {
                const header = document.createElement('div');
                header.className = 'gui-header';
                header.style.cssText = `
                    background: linear-gradient(90deg, rgba(0, 30, 60, 0.9), rgba(0, 50, 90, 0.9));
                    padding: 12px 16px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    cursor: move;
                    user-select: none;
                    flex-shrink: 0;
                    border-bottom: 1px solid rgba(0, 242, 255, 0.2);
                `;

                // Title section
                const titleSection = document.createElement('div');
                titleSection.style.cssText = `
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    color: var(--primary-color);
                    font-family: 'Orbitron', sans-serif;
                    font-size: 14px;
                    font-weight: 600;
                `;

                titleSection.innerHTML = `
                    <span style="font-size: 18px;">${config.icon}</span>
                    <span>${config.title}</span>
                `;

                // Controls section
                const controls = document.createElement('div');
                controls.style.cssText = `
                    display: flex;
                    gap: 8px;
                `;

                // Minimize button
                const minimizeBtn = this.createControlButton('−', '#f7d31b', () => {
                    this.minimizeGui(guiType);
                });

                // Maximize button
                const maximizeBtn = this.createControlButton('□', '#1bf7cd', () => {
                    this.toggleMaximize(guiType);
                });

                // Close button
                const closeBtn = this.createControlButton('×', '#f73a1b', () => {
                    this.closeGui(guiType);
                });

                controls.appendChild(minimizeBtn);
                controls.appendChild(maximizeBtn);
                controls.appendChild(closeBtn);

                header.appendChild(titleSection);
                header.appendChild(controls);

                return header;
            }

            createControlButton(text, color, onClick) {
                const button = document.createElement('button');
                button.textContent = text;
                button.style.cssText = `
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    border: none;
                    background: ${color};
                    color: rgba(0, 0, 0, 0.7);
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    font-weight: bold;
                    transition: all 0.2s ease;
                `;

                button.addEventListener('click', onClick);
                button.addEventListener('mouseenter', () => {
                    button.style.transform = 'scale(1.1)';
                });
                button.addEventListener('mouseleave', () => {
                    button.style.transform = 'scale(1)';
                });

                return button;
            }

            calculatePosition(guiType) {
                // Calculate position to avoid overlap
                const baseX = 100;
                const baseY = 100;
                const offset = this.loadedGuis.size * 30;

                return {
                    x: baseX + offset,
                    y: baseY + offset
                };
            }

            makeInteractive(container) {
                const header = container.querySelector('.gui-header');

                // Make draggable
                let isDragging = false;
                let startX, startY, startLeft, startTop;

                header.addEventListener('mousedown', (e) => {
                    if (e.target.tagName === 'BUTTON') return; // Don't drag when clicking buttons

                    isDragging = true;
                    startX = e.clientX;
                    startY = e.clientY;
                    startLeft = container.offsetLeft;
                    startTop = container.offsetTop;

                    this.bringToFront(container);

                    document.addEventListener('mousemove', onMouseMove);
                    document.addEventListener('mouseup', onMouseUp);

                    e.preventDefault();
                });

                const onMouseMove = (e) => {
                    if (!isDragging) return;

                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;

                    container.style.left = `${startLeft + deltaX}px`;
                    container.style.top = `${startTop + deltaY}px`;
                };

                const onMouseUp = () => {
                    isDragging = false;
                    document.removeEventListener('mousemove', onMouseMove);
                    document.removeEventListener('mouseup', onMouseUp);
                };

                // Make resizable (simple corner resize)
                const resizeHandle = document.createElement('div');
                resizeHandle.style.cssText = `
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    width: 20px;
                    height: 20px;
                    cursor: nwse-resize;
                    background: var(--primary-color);
                    opacity: 0.3;
                    border-radius: 0 0 12px 0;
                    transition: opacity 0.2s ease;
                `;

                resizeHandle.addEventListener('mouseenter', () => {
                    resizeHandle.style.opacity = '0.6';
                });

                resizeHandle.addEventListener('mouseleave', () => {
                    resizeHandle.style.opacity = '0.3';
                });

                container.appendChild(resizeHandle);
            }

            bringToFront(container) {
                container.style.zIndex = this.zIndexCounter++;
            }

            setupGuiCommunication(guiType, iframe) {
                iframe.onload = () => {
                    try {
                        const iframeWindow = iframe.contentWindow;

                        // Share database reference
                        if (window.AgentLeeDB) {
                            iframeWindow.AgentLeeDB = window.AgentLeeDB;
                        }

                        // Share event bus
                        if (window.AgentLeeEventBus) {
                            iframeWindow.AgentLeeEventBus = window.AgentLeeEventBus;
                        }

                        // Share helpers
                        if (window.AgentLeeDBHelpers) {
                            iframeWindow.AgentLeeDBHelpers = window.AgentLeeDBHelpers;
                        }

                        // Share Echo Dispatcher
                        if (window.EchoDispatcher) {
                            iframeWindow.EchoDispatcher = window.EchoDispatcher;
                        }

                        console.log(`✅ GUI communication setup for ${guiType}`);

                    } catch (error) {
                        console.warn(`⚠️ Could not setup communication for ${guiType}:`, error);
                    }
                };
            }

            async saveGuiState(guiType, container) {
                if (!window.AgentLeeDBHelpers) return;

                const rect = container.getBoundingClientRect();
                const guiState = {
                    gui_id: container.dataset.guiId,
                    region: guiType,
                    status: 'active',
                    position_x: rect.left,
                    position_y: rect.top,
                    width: rect.width,
                    height: rect.height,
                    created_at: new Date().toISOString(),
                    last_updated: new Date().toISOString(),
                    z_index: container.style.zIndex
                };

                try {
                    await window.AgentLeeDBHelpers.addRecord('gui_registry', guiState);
                    console.log(`✅ GUI state saved for ${guiType}`);
                } catch (error) {
                    console.error(`❌ Failed to save GUI state for ${guiType}:`, error);
                }
            }

            minimizeGui(guiType) {
                const gui = this.loadedGuis.get(guiType);
                if (!gui) return;

                gui.container.style.display = 'none';

                // Add to minimized tray (implement if needed)
                console.log(`📦 Minimized GUI: ${guiType}`);
            }

            toggleMaximize(guiType) {
                const gui = this.loadedGuis.get(guiType);
                if (!gui) return;

                const container = gui.container;
                const isMaximized = container.dataset.maximized === 'true';

                if (isMaximized) {
                    // Restore
                    container.style.width = gui.config.defaultSize.width + 'px';
                    container.style.height = gui.config.defaultSize.height + 'px';
                    container.style.left = '100px';
                    container.style.top = '100px';
                    container.dataset.maximized = 'false';
                } else {
                    // Maximize
                    container.style.width = '100vw';
                    container.style.height = '100vh';
                    container.style.left = '0px';
                    container.style.top = '0px';
                    container.dataset.maximized = 'true';
                }

                this.bringToFront(container);
            }

            closeGui(guiType) {
                const gui = this.loadedGuis.get(guiType);
                if (!gui) return;

                // Remove from DOM
                gui.container.remove();

                // Remove from loaded GUIs
                this.loadedGuis.delete(guiType);

                console.log(`❌ Closed GUI: ${guiType}`);
            }

            getLoadedGuis() {
                return Array.from(this.loadedGuis.keys());
            }
        }

        // Initialize global MicrofrontendLoader
        window.MicrofrontendLoader = new MicrofrontendLoader();

        // Initialize IndexedDB Database with Production Schema Management
        const createAgentLeeDatabase = async () => {
            return new Promise((resolve, reject) => {
                const currentVersion = window.AgentLeeSchemaManager?.getCurrentVersion() || 3;
                const request = indexedDB.open('AgentLeeDB', currentVersion);

                request.onupgradeneeded = async (event) => {
                    const db = event.target.result;
                    const oldVersion = event.oldVersion;
                    const newVersion = event.newVersion;
                    const transaction = event.target.transaction;

                    console.log(`🔧 Database upgrade needed: v${oldVersion} → v${newVersion}`);

                    try {
                        // Use schema migration system if available
                        if (window.AgentLeeSchemaManager) {
                            await window.AgentLeeSchemaManager.performMigration(
                                db, oldVersion, newVersion, transaction
                            );
                        } else {
                            // Fallback to basic schema creation
                            console.log('⚠️ Schema manager not available, using basic schema');

                            // Basic tables for index.html (subset of full schema)
                            if (!db.objectStoreNames.contains('agents')) {
                                const agents = db.createObjectStore('agents', { keyPath: 'agent_id' });
                                agents.createIndex('name', 'name', { unique: false });
                                agents.createIndex('status', 'status', { unique: false });
                            }

                            if (!db.objectStoreNames.contains('tasks')) {
                                const tasks = db.createObjectStore('tasks', { keyPath: 'task_id' });
                                tasks.createIndex('assigned_to', 'assigned_to', { unique: false });
                                tasks.createIndex('status', 'status', { unique: false });
                                tasks.createIndex('trigger_region', 'trigger_region', { unique: false });
                            }

                            if (!db.objectStoreNames.contains('workers')) {
                                const workers = db.createObjectStore('workers', { keyPath: 'worker_id' });
                                workers.createIndex('type', 'type', { unique: false });
                                workers.createIndex('status', 'status', { unique: false });
                            }

                            if (!db.objectStoreNames.contains('llm_sessions')) {
                                const sessions = db.createObjectStore('llm_sessions', { keyPath: 'session_id' });
                                sessions.createIndex('timestamp', 'timestamp', { unique: false });
                                sessions.createIndex('context_scope', 'context_scope', { multiEntry: true });
                                sessions.createIndex('llm_type', 'llm_type', { unique: false });
                            }

                            if (!db.objectStoreNames.contains('telemetry')) {
                                const telemetry = db.createObjectStore('telemetry', { keyPath: 'entry_id' });
                                telemetry.createIndex('region', 'region', { unique: false });
                                telemetry.createIndex('timestamp', 'timestamp', { unique: false });
                                telemetry.createIndex('event_type', 'event_type', { unique: false });
                            }
                        }

                    } catch (error) {
                        console.error('❌ Database migration failed:', error);
                        reject(error);
                        return;
                    }
                };

                request.onsuccess = async () => {
                    const db = request.result;
                    console.log('✅ Agent Lee IndexedDB successfully initialized');
                    window.AgentLeeDB = db;

                    // Validate schema if schema manager is available
                    if (window.AgentLeeSchemaManager) {
                        const validation = await window.AgentLeeSchemaManager.validateSchema(db);
                        if (!validation.valid) {
                            console.warn('⚠️ Schema validation issues detected:', validation);
                        }
                    }

                    // Initialize event bus for index.html
                    initializeIndexEventBus();

                    resolve(db);
                };

                request.onerror = () => {
                    console.error('❌ Database error:', request.error);
                    reject(request.error);
                };
            });
        };

        // Event bus initialization for index.html
        function initializeIndexEventBus() {
            if (!window.AgentLeeEventBus) {
                window.AgentLeeEventBus = new BroadcastChannel('AgentLeeSync');

                window.AgentLeeEventBus.onmessage = function(event) {
                    console.log('📨 Index received event:', event.data);
                    handleIndexEvent(event.data);
                };

                console.log('✅ Index Event Bus initialized');
            }
        }

        // Handle events in index.html
        function handleIndexEvent(eventData) {
            switch(eventData.type) {
                case 'TaskUpdated':
                    // Update any task displays in index
                    break;
                case 'AgentStatusChanged':
                    // Update agent status in main interface
                    break;
                case 'LLMSessionStarted':
                    // Show LLM activity indicator
                    break;
            }
        }

        // Main App Component
        function AgentLeeApp() {
            // State variables
            const [messages, setMessages] = useState([]);
            const [inputText, setInputText] = useState('');
            const [isTalking, setIsTalking] = useState(false);
            const [isRecording, setIsRecording] = useState(false);
            const [theme, setTheme] = useState('dark');
            const [language, setLanguage] = useState('en-US');
            const [floatingGuis, setFloatingGuis] = useState([]);
            const [nextGuiId, setNextGuiId] = useState(1);
            const [dialerNumber, setDialerNumber] = useState('');
            const [cameraStream, setCameraStream] = useState(null);
            const [database, setDatabase] = useState(null);
            const [minimizedGuis, setMinimizedGuis] = useState([]);
            const [agentCardMinimized, setAgentCardMinimized] = useState(false);
            
            // Refs
            const speechRecognition = useRef(null);
            const agentCardRef = useRef(null);
            
            // ===== CRITICAL: FRONTEND LLM LOADING & ERROR HANDLING =====

            // Frontend LLM Status Management
            const [llmStatus, setLlmStatus] = useState({
                phi3: { loaded: false, error: null, loading: false },
                gemini: { loaded: false, error: null, loading: false },
                qwen: { loaded: false, error: null, loading: false },
                webllm: { loaded: false, error: null, loading: false }
            });

            // Load Frontend LLMs with Error Handling
            const loadFrontendLLMs = async () => {
                console.log('🧠 Loading frontend LLMs...');

                // Load PHI-3 (Local Fine-tuned Model)
                try {
                    setLlmStatus(prev => ({ ...prev, phi3: { ...prev.phi3, loading: true } }));

                    // Attempt to load PHI-3 from local files
                    // This would load from llama_models/fine_tuned_phi3/
                    console.log('Loading PHI-3 from local files...');

                    // Simulate loading (replace with actual model loading)
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    setLlmStatus(prev => ({
                        ...prev,
                        phi3: { loaded: true, error: null, loading: false }
                    }));

                    console.log('✅ PHI-3 loaded successfully');

                } catch (error) {
                    console.error('❌ PHI-3 loading failed:', error);
                    setLlmStatus(prev => ({
                        ...prev,
                        phi3: { loaded: false, error: error.message, loading: false }
                    }));
                }

                // Load Gemini SDK
                try {
                    setLlmStatus(prev => ({ ...prev, gemini: { ...prev.gemini, loading: true } }));

                    // Check if Gemini SDK is available
                    if (window.google && window.google.generativeai) {
                        setLlmStatus(prev => ({
                            ...prev,
                            gemini: { loaded: true, error: null, loading: false }
                        }));
                        console.log('✅ Gemini SDK available');
                    } else {
                        throw new Error('Gemini SDK not loaded');
                    }

                } catch (error) {
                    console.error('❌ Gemini SDK not available:', error);
                    setLlmStatus(prev => ({
                        ...prev,
                        gemini: { loaded: false, error: 'Gemini SDK not available', loading: false }
                    }));
                }

                // Load WebLLM
                try {
                    setLlmStatus(prev => ({ ...prev, webllm: { ...prev.webllm, loading: true } }));

                    // Check if WebLLM is available
                    if (window.webllm) {
                        setLlmStatus(prev => ({
                            ...prev,
                            webllm: { loaded: true, error: null, loading: false }
                        }));
                        console.log('✅ WebLLM available');
                    } else {
                        throw new Error('WebLLM not loaded');
                    }

                } catch (error) {
                    console.error('❌ WebLLM not available:', error);
                    setLlmStatus(prev => ({
                        ...prev,
                        webllm: { loaded: false, error: 'WebLLM not available', loading: false }
                    }));
                }

                // Update Echo Dispatcher with LLM status
                if (window.EchoDispatcher) {
                    window.EchoDispatcher.llmStatus = llmStatus;
                }
            };

            // Display LLM Status in UI
            const displayLLMStatus = () => {
                const statusMessages = [];

                Object.entries(llmStatus).forEach(([llmType, status]) => {
                    if (status.loading) {
                        statusMessages.push(`${llmType.toUpperCase()}: Loading...`);
                    } else if (status.loaded) {
                        statusMessages.push(`${llmType.toUpperCase()}: ✅ Ready`);
                    } else if (status.error) {
                        statusMessages.push(`${llmType.toUpperCase()}: ❌ ${status.error}`);
                    }
                });

                return statusMessages.join(' | ');
            };

            // Initialize everything with complete Agent Lee integration
            useEffect(() => {
                initializeAgentLeeSystem();
            }, []);

            const initializeAgentLeeSystem = async () => {
                console.log('🚀 Initializing complete Agent Lee system...');

                try {
                    // 1. Load schemas and basic setup
                    loadAgentLeeSchemas();
                    speechRecognition.current = initSpeechRecognition();

                    // 2. Initialize database with production schema
                    console.log('🔄 Initializing database...');
                    const db = await createAgentLeeDatabase();
                    setDatabase(db);

                    // 2.5. Initialize Database Helpers
                    console.log('🔄 Initializing database helpers...');
                    window.AgentLeeDBHelpers = {
                        async addRecord(table, data) {
                            try {
                                const transaction = db.transaction([table], 'readwrite');
                                const store = transaction.objectStore(table);
                                const result = await store.add(data);
                                console.log(`✅ Added record to ${table}:`, data);
                                return { success: true, id: result };
                            } catch (error) {
                                console.error(`❌ Failed to add record to ${table}:`, error);
                                return { success: false, error: error.message };
                            }
                        },

                        async getRecords(table, filter = {}) {
                            try {
                                const transaction = db.transaction([table], 'readonly');
                                const store = transaction.objectStore(table);
                                const result = await store.getAll();
                                console.log(`📖 Retrieved ${result.length} records from ${table}`);
                                return { success: true, data: result };
                            } catch (error) {
                                console.error(`❌ Failed to get records from ${table}:`, error);
                                return { success: false, error: error.message };
                            }
                        },

                        async updateRecord(table, id, data) {
                            try {
                                const transaction = db.transaction([table], 'readwrite');
                                const store = transaction.objectStore(table);
                                await store.put({ ...data, [store.keyPath]: id });
                                console.log(`✏️ Updated record in ${table}:`, id);
                                return { success: true };
                            } catch (error) {
                                console.error(`❌ Failed to update record in ${table}:`, error);
                                return { success: false, error: error.message };
                            }
                        }
                    };
                    console.log('✅ Database helpers initialized');

                    // 2.7. Initialize AgentLee.API.AZRBridge Module
                    console.log('🔄 Initializing AgentLee.API.AZRBridge...');
                    window.AgentLee = window.AgentLee || {};
                    window.AgentLee.API = window.AgentLee.API || {};
                    window.AgentLee.Utils = window.AgentLee.Utils || {};

                    // === ✅ AgentLee.API.AZRBridge: Deprecated - Use StreamlinedLLMManager ===
                    window.AgentLee.API.AZRBridge = {
                        sendTask: (prompt) => {
                            console.log('⚠️ AZRBridge deprecated - use StreamlinedLLMManager.reason() instead');
                            if (window.StreamlinedLLMManager) {
                                return window.StreamlinedLLMManager.reason(prompt);
                            }
                            return Promise.resolve({ result: 'Use StreamlinedLLMManager instead' });
                        },
                        getStatus: () => ({ connected: false, deprecated: true, queueLength: 0 }),
                        isConnected: () => false,
                        ping: () => console.log('⚠️ AZRBridge deprecated'),
                        reconnect: () => console.log('⚠️ AZRBridge deprecated')
                    };



                    // ✅ AZR Response Handler - Now uses StreamlinedLLMManager
                    window.AgentLee.API.onAZRResponse = function (result) {
                        console.log("💡 AZR response received (deprecated handler):", result);
                        // This handler is deprecated - responses now come through StreamlinedLLMManager
                    };

                    console.log('✅ AgentLee.API.AZRBridge placeholder initialized (deprecated)');

                    // 3. AZR Bridge is now deprecated - using Streamlined LLM Manager
                    console.log('✅ AZR functionality available via Streamlined LLM Manager');

                    // 4. Initialize Streamlined LLM System
                    console.log('🔄 Initializing Streamlined LLM System...');

                    // Wait a moment for the module to load
                    let attempts = 0;
                    while (!window.StreamlinedLLMManager && attempts < 10) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                        attempts++;
                    }

                    if (window.StreamlinedLLMManager) {
                        await window.StreamlinedLLMManager.initialize();
                        console.log('✅ Streamlined LLM System initialized');

                        // Report status after initialization
                        setTimeout(() => reportLLMStatus(), 2000);

                        // Setup LLM event listeners
                        window.addEventListener('llmSystemReady', (event) => {
                            console.log('✅ LLM System Ready:', event.detail);
                            reportLLMStatus();
                        });

                        window.addEventListener('llmReady', (event) => {
                            const { model, hemisphere } = event.detail;
                            console.log(`✅ ${model.toUpperCase()} ready in ${hemisphere} hemisphere`);

                            if (window.flashHemisphere) {
                                window.flashHemisphere(hemisphere, `${model.toUpperCase()} Ready`);
                            }
                        });

                        window.addEventListener('llmLoadingProgress', (event) => {
                            const { model, progress } = event.detail;
                            console.log(`📥 ${model.toUpperCase()} loading: ${progress}%`);
                        });

                    } else {
                        console.warn('⚠️ Streamlined LLM Manager not available after waiting');
                    }

                    // 5. Initialize Cognitive Matrix
                    console.log('🔄 Activating Cognitive Matrix...');
                    if (window.CognitiveMatrix) {
                        // Test cognitive processing
                        const testThought = await window.CognitiveMatrix.processThought(
                            'System initialization complete',
                            { source: 'system_startup' }
                        );
                        console.log('🧠 Cognitive Matrix test:', testThought);
                    }

                    // 6. Initialize Memory System
                    console.log('🔄 Activating Memory System...');
                    if (window.AgentMemorySystem) {
                        await window.AgentMemorySystem.remember(
                            'system_startup',
                            'Agent Lee system successfully initialized',
                            'episodic',
                            { emotionalWeight: 0.8, contextTags: ['system', 'startup'] }
                        );
                    }

                    // 7. Agent Lee greeting with cognitive processing
                    const greeting = "Hello! I'm Agent Lee, your cognitive AI assistant. My neural systems are online and ready to assist.";
                    setMessages([{
                        type: 'agent',
                        content: greeting,
                        timestamp: new Date().toLocaleTimeString()
                    }]);

                    // Process greeting through cognitive matrix
                    if (window.CognitiveMatrix) {
                        window.CognitiveMatrix.processThought(greeting, {
                            source: 'agent_greeting',
                            type: 'system_message'
                        });
                    }

                    // Speak greeting
                    speak(greeting, language);

                    // 8. Setup UI interactions
                    if (agentCardRef.current) {
                        const header = agentCardRef.current.querySelector('.card-header');
                        setupDraggable(agentCardRef.current, header);
                    }
                    setupInteractJS();

                    // 9. Broadcast system ready event
                    if (window.AgentLeeEventBus) {
                        window.AgentLeeEventBus.postMessage({
                            type: 'SystemInitialized',
                            data: {
                                timestamp: new Date().toISOString(),
                                components: [
                                    'Database',
                                    'AZR Bridge',
                                    'Frontend LLMs',
                                    'Cognitive Matrix',
                                    'Memory System',
                                    'Production Monitoring'
                                ]
                            }
                        });
                    }

                    console.log('✅ Agent Lee system initialization complete!');

                } catch (error) {
                    console.error('❌ Agent Lee system initialization failed:', error);

                    // Show error message to user
                    setMessages([{
                        type: 'agent',
                        content: 'I encountered an issue during initialization. Some features may be limited. Please check the console for details.',
                        timestamp: new Date().toLocaleTimeString()
                    }]);
                }
            };

            // Streamlined LLM status reporting
            const reportLLMStatus = () => {
                if (window.StreamlinedLLMManager) {
                    const status = window.StreamlinedLLMManager.getAllModelStatus();
                    const availableModels = window.StreamlinedLLMManager.getAvailableModels();

                    const statusMessage = `
🚀 Agent Lee System Status:
• Database: ✅ Operational (22 tables)
• AZR Model: ${window.azrLLM?.isLoaded ? '✅ Loaded (Wllama)' : '⚠️ Loading...'}
• LLM Models: ${availableModels.length}/5 loaded
• Cognitive Matrix: ✅ Active
• Memory System: ✅ Active
• Production Monitoring: ✅ Active
• System Health: ✅ Monitoring
• Camera Diagnostics: ✅ Ready

Available Models: ${availableModels.map(m => `${m.name} (${m.hemisphere})`).join(', ')}
${availableModels.length === 0 ? '⚠️ No LLMs loaded - check console for errors' : ''}
${!window.azrLLM?.isLoaded ? '⚠️ AZR model not loaded - check model URL in azr.js' : ''}`;

                    console.log(statusMessage);

                    // Update activity ticker
                    if (window.updateActivityTicker) {
                        window.updateActivityTicker(`LLM System: ${availableModels.length}/5 models ready`);
                    }
                } else {
                    console.log('⚠️ Streamlined LLM Manager not available yet');
                }
            };

            // Setup interact.js for draggable and resizable elements
            const setupInteractJS = () => {
                if (window.interact) {
                    // Will be configured for each GUI when it's created
                    console.log('✅ interact.js loaded and ready');
                } else {
                    console.error('❌ interact.js not loaded');
                }
            };

            // Update theme
            useEffect(() => {
                document.body.setAttribute('data-theme', theme);
            }, [theme]);
            
            // Text to speech function
            const speak = (text, language = 'en-US') => {
                if ('speechSynthesis' in window) {
                    setIsTalking(true);
                    const utterance = new SpeechSynthesisUtterance(text);
                    utterance.rate = 0.9;
                    utterance.pitch = 1.1;
                    utterance.lang = language;
                    utterance.onend = () => setIsTalking(false);
                    speechSynthesis.speak(utterance);
                    
                    // Add to chat as Agent response
                    addMessage('agent', text);
                }
            };

            // Handle avatar click
            const handleAvatarClick = () => {
                if (agentCardMinimized) {
                    setAgentCardMinimized(false);
                    return;
                }
                
                const responses = [
                    "Hello! How can I assist you today?",
                    "My cognitive systems are fully operational. What would you like to explore?",
                    "I'm ready to help with any tasks or questions you have.",
                    "Agent Lee at your service. How may I assist you?",
                    "I'm here and listening. What would you like to do?"
                ];
                const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                addMessage('agent', randomResponse);
                speak(randomResponse, language);
            };

            // Add message to chat
            const addMessage = (type, content) => {
                const newMessage = {
                    type,
                    content,
                    timestamp: new Date().toLocaleTimeString()
                };
                setMessages(prev => [...prev, newMessage]);
            };

            // Handle chat input with LLM routing
            const handleSendMessage = async () => {
                if (inputText.trim()) {
                    addMessage('user', inputText);

                    try {
                        // Use Streamlined LLM Manager for intelligent responses
                        let response;

                        if (inputText.toLowerCase().includes('search for') || inputText.toLowerCase().startsWith('find')) {
                            const query = inputText.replace(/search for|find/i, '').trim();
                            if (query) {
                                const searchData = searchWithGoogle(query);
                                createFloatingGui('search', searchData);
                                speak(`Searching for: ${query}`, language);
                                return;
                            }
                        } else if (window.StreamlinedLLMManager) {
                            // Check if we have the streamlined system
                            if (inputText.toLowerCase().includes('reason') || inputText.toLowerCase().includes('solve')) {
                                // Use AZR for reasoning tasks
                                try {
                                    const reasoningResult = await window.StreamlinedLLMManager.reason(inputText);
                                    response = reasoningResult.solution || reasoningResult.text || 'I need more information to solve this problem.';
                                } catch (error) {
                                    console.warn('Reasoning failed, using chat fallback');
                                    const chatResult = await window.StreamlinedLLMManager.chat(inputText);
                                    response = chatResult.text;
                                }
                            } else {
                                // Use chat for general conversation
                                const chatResult = await window.StreamlinedLLMManager.chat(inputText);
                                response = chatResult.text || 'I understand your message.';
                            }
                        } else {
                            // Fallback if streamlined system not available
                            response = `I received your message: "${inputText}". The LLM system is still loading, please try again in a moment.`;
                        }

                        if (response) {
                            addMessage('agent', response);
                            speak(response, language);
                        }

                            // Log successful LLM session
                            if (window.AgentLeeDBHelpers) {
                                await window.AgentLeeDBHelpers.addRecord('llm_sessions', {
                                    session_id: `session_${Date.now()}`,
                                    prompt: inputText,
                                    response: result.result,
                                    llm_type: result.source,
                                    token_usage: result.result ? result.result.length : 0,
                                    context_scope: ['chat', 'user_input'],
                                    timestamp: new Date().toISOString()
                                });

                                // Broadcast LLM session event
                                if (window.AgentLeeEventBus) {
                                    window.AgentLeeEventBus.postMessage({
                                        type: 'LLMSessionStarted',
                                        data: {
                                            llm_type: result.source,
                                            input: inputText,
                                            timestamp: new Date().toISOString()
                                        }
                                    });
                                }
                            }

                        } else {
                            // Handle LLM processing error
                            const errorResponse = `I encountered an issue processing that request: ${result.error}`;
                            addMessage('agent', errorResponse);
                            speak(errorResponse, language);
                        }

                    } catch (error) {
                        console.error('LLM processing error:', error);
                        const fallbackResponse = `I'm having trouble processing that right now. Please try again.`;
                        addMessage('agent', fallbackResponse);
                        speak(fallbackResponse, language);
                    }

                    setInputText('');
                }
            };

            // Handle Enter key
            const handleKeyPress = (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                }
            };

            // Communication functions
            const toggleTalking = () => {
                if (isTalking) {
                    // Stop speaking
                    if ('speechSynthesis' in window) {
                        speechSynthesis.cancel();
                        setIsTalking(false);
                    }
                } else {
                    // Start speaking
                    speak("I'm listening. What can I help you with?", language);
                }
            };

            const toggleRecording = () => {
                if (!isRecording) {
                    // Start voice recognition with LLM routing
                    listenAndSearch(
                        speechRecognition.current,
                        async (voiceQuery) => {
                            addMessage('user', voiceQuery);

                            try {
                                // Use Echo System Dispatcher for voice input
                                const result = await window.EchoDispatcher.executeTask(voiceQuery, {
                                    sessionId: Date.now().toString(),
                                    source: 'voice_input',
                                    language: language
                                });

                                if (result.success) {
                                    // Handle voice command results
                                    if (voiceQuery.toLowerCase().includes('search')) {
                                        const searchData = searchWithGoogle(voiceQuery);
                                        createFloatingGui('search', searchData);
                                        speak(`Searching for: ${voiceQuery}`, language);
                                    } else {
                                        const response = result.result || `Voice command processed by ${result.source}`;
                                        addMessage('agent', response);
                                        speak(response, language);
                                    }
                                } else {
                                    const errorResponse = `I had trouble processing that voice command: ${result.error}`;
                                    addMessage('agent', errorResponse);
                                    speak(errorResponse, language);
                                }

                            } catch (error) {
                                console.error('Voice processing error:', error);
                                addMessage('agent', 'Sorry, I had trouble understanding. Please try again.');
                                speak('Sorry, I had trouble understanding. Please try again.', language);
                            }

                            setIsRecording(false);
                        },
                        (error) => {
                            console.error('Voice recognition error:', error);
                            addMessage('agent', 'Sorry, I had trouble with voice recognition. Please try again.');
                            speak('Sorry, I had trouble with voice recognition. Please try again.', language);
                            setIsRecording(false);
                        }
                    );
                    setIsRecording(true);
                } else {
                    // Stop recording
                    if (speechRecognition.current) {
                        speechRecognition.current.abort();
                    }
                    setIsRecording(false);
                }
            };

            // Minimize Agent Card
            const minimizeAgentCard = () => {
                setAgentCardMinimized(true);
            };
            
            // Create a floating GUI - Enhanced with MicrofrontendLoader
            const createFloatingGui = async (type, data = null) => {
                try {
                    // Use MicrofrontendLoader for brain regions and core GUIs
                    if (['agents', 'db', 'todo', 'workers', 'llmcenter', 'logs'].includes(type)) {
                        const gui = await window.MicrofrontendLoader.loadGui(type, data);

                        // Voice feedback
                        const voiceResponse = regionVoiceMap[type] || `Opening ${type} interface.`;
                        speak(voiceResponse, language);

                        return gui;
                    }

                    // Handle other GUI types with existing logic
                    let guiConfig = {};

                    switch(type) {
                        case 'settings':
                            // Use LLM Center for settings
                            return await window.MicrofrontendLoader.loadGui('llmcenter', data);

                        case 'call':
                            guiConfig = {
                                title: '📞 COMMUNICATION MODULE',
                                content: 'call',
                                width: 400,
                                height: 600
                            };
                            break;
                        case 'sms':
                            // Direct link to Telegram Web as requested
                            guiConfig = {
                                title: '💬 TELEGRAM',
                                url: TELEGRAM_URL,
                                width: 600,
                                height: 500
                            };
                            break;
                        case 'search':
                            // Direct link to Google Search URL as requested
                            guiConfig = {
                                title: data?.title || '🔍 NATURAL LANGUAGE SEARCH',
                                url: data?.url || GOOGLE_SEARCH_URL,
                                width: data?.width || 1200,
                                height: data?.height || 800
                            };
                            break;
                        case 'camera':
                            guiConfig = {
                                title: '📷 CAMERA',
                                content: 'camera',
                                width: 500,
                                height: 400
                            };
                            break;
                        case 'files':
                            guiConfig = {
                                title: '📁 FILE EXPLORER',
                                content: 'files',
                                width: 500,
                                height: 400
                            };
                            break;
                        default:
                            console.warn(`Unknown GUI type: ${type}`);
                            return null;
                    }

                    // Calculate position - to left of Agent Card (unless Agent Card is minimized)
                    let posX = 100;
                    let posY = 100;

                    if (agentCardRef.current && !agentCardMinimized) {
                        const rect = agentCardRef.current.getBoundingClientRect();
                        posX = Math.max(50, rect.left - guiConfig.width - 30);
                        posY = rect.top;

                        // If too close to edge, adjust
                        if (posX < 20) {
                            posX = 20;
                            posY += 50; // Offset vertically if horizontal space is tight
                        }
                    } else if (agentCardMinimized) {
                        // If Agent Card is minimized, center the GUI
                        posX = (window.innerWidth - guiConfig.width) / 2;
                        posY = (window.innerHeight - guiConfig.height) / 2;
                    }

                    const newGui = {
                        id: nextGuiId,
                        ...guiConfig,
                        minimized: false,
                        fullscreen: false,
                        position: {
                            x: posX,
                            y: posY
                        },
                        type: type
                    };

                    setFloatingGuis(prev => [...prev, newGui]);
                    setNextGuiId(prev => prev + 1);

                    // Voice feedback
                    const voiceResponse = regionVoiceMap[type] || `Opening ${type} interface.`;
                    speak(voiceResponse, language);

                    return newGui.id;

                } catch (error) {
                    console.error(`Failed to create floating GUI ${type}:`, error);
                    speak(`Sorry, I couldn't open the ${type} interface.`, language);
                    return null;
                }
            };
            
            // Toggle fullscreen for GUI
            const toggleFullscreen = (id) => {
                setFloatingGuis(prev => prev.map(gui => {
                    if (gui.id === id) {
                        const willBeFullscreen = !gui.fullscreen;
                        
                        // If going fullscreen, minimize Agent Card
                        if (willBeFullscreen) {
                            setAgentCardMinimized(true);
                        }
                        
                        return {...gui, fullscreen: willBeFullscreen};
                    }
                    return gui;
                }));
            };
            
            // Configure draggable GUI
            const configureGuiInteractions = (element, guiId) => {
                if (!window.interact || !element) return;
                
                const gui = floatingGuis.find(g => g.id === guiId);
                if (gui?.fullscreen) return; // Don't make fullscreen GUIs draggable
                
                // Make draggable
                window.interact(element)
                    .draggable({
                        inertia: true,
                        modifiers: [
                            window.interact.modifiers.restrictRect({
                                restriction: 'parent',
                                endOnly: true
                            })
                        ],
                        autoScroll: true,
                        listeners: {
                            move: dragMoveListener,
                            end: (event) => {
                                const target = event.target;
                                const guiId = parseInt(target.getAttribute('data-gui-id'));
                                const x = parseFloat(target.getAttribute('data-x')) || 0;
                                const y = parseFloat(target.getAttribute('data-y')) || 0;
                                
                                // Update state with new position
                                setFloatingGuis(prev => prev.map(gui => 
                                    gui.id === guiId ? {...gui, position: {x, y}} : gui
                                ));
                            }
                        }
                    })
                    .resizable({
                        edges: { right: true, bottom: true, left: false, top: false },
                        modifiers: [
                            window.interact.modifiers.restrictSize({
                                min: { width: 300, height: 200 }
                            })
                        ],
                        listeners: {
                            move: resizeMoveListener
                        }
                    });
                
                function dragMoveListener(event) {
                    const target = event.target;
                    const guiId = parseInt(target.getAttribute('data-gui-id'));
                    
                    // Keep the dragged position in the data-x/data-y attributes
                    const x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx;
                    const y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy;
                    
                    // Translate the element
                    target.style.transform = `translate(${x}px, ${y}px)`;
                    
                    // Update the position attributes
                    target.setAttribute('data-x', x);
                    target.setAttribute('data-y', y);
                }
                
                function resizeMoveListener(event) {
                    const target = event.target;
                    const guiId = parseInt(target.getAttribute('data-gui-id'));
                    
                    let x = parseFloat(target.getAttribute('data-x')) || 0;
                    let y = parseFloat(target.getAttribute('data-y')) || 0;
                    
                    // Update the element's style
                    target.style.width = `${event.rect.width}px`;
                    target.style.height = `${event.rect.height}px`;
                    
                    // Translate when resizing from top or left edges
                    x += event.deltaRect.left;
                    y += event.deltaRect.top;
                    
                    target.style.transform = `translate(${x}px, ${y}px)`;
                    
                    target.setAttribute('data-x', x);
                    target.setAttribute('data-y', y);
                }
            };
            
            // Setup simple draggable element
            const setupDraggable = (element, handle) => {
                if (!element || !handle) return;
                
                let isDragging = false;
                let startX, startY, startLeft, startTop;
                
                const onMouseDown = (e) => {
                    if (agentCardMinimized) return; // Don't drag when minimized
                    
                    isDragging = true;
                    startX = e.clientX;
                    startY = e.clientY;
                    startLeft = element.offsetLeft;
                    startTop = element.offsetTop;
                    
                    handle.style.cursor = 'grabbing';
                    
                    document.addEventListener('mousemove', onMouseMove);
                    document.addEventListener('mouseup', onMouseUp);
                    
                    e.preventDefault();
                };
                
                const onMouseMove = (e) => {
                    if (!isDragging) return;
                    
                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;
                    
                    element.style.left = `${startLeft + deltaX}px`;
                    element.style.top = `${startTop + deltaY}px`;
                };
                
                const onMouseUp = () => {
                    isDragging = false;
                    handle.style.cursor = 'grab';
                    
                    document.removeEventListener('mousemove', onMouseMove);
                    document.removeEventListener('mouseup', onMouseUp);
                };
                
                handle.addEventListener('mousedown', onMouseDown);
                
                return () => {
                    handle.removeEventListener('mousedown', onMouseDown);
                };
            };
            
            // Close a floating GUI
            const closeFloatingGui = (id) => {
                setFloatingGuis(prev => prev.filter(gui => gui.id !== id));
                
                // Stop camera if it was a camera GUI
                const guiToClose = floatingGuis.find(gui => gui.id === id);
                if (guiToClose && guiToClose.type === 'camera' && cameraStream) {
                    cameraStream.getTracks().forEach(track => track.stop());
                    setCameraStream(null);
                }
            };
            
            // Minimize a floating GUI
            const minimizeFloatingGui = (id) => {
                const guiToMinimize = floatingGuis.find(gui => gui.id === id);
                
                if (guiToMinimize) {
                    // Add to minimized tray
                    setMinimizedGuis(prev => [...prev, guiToMinimize]);
                    
                    // Remove from floating GUIs
                    setFloatingGuis(prev => prev.filter(gui => gui.id !== id));
                }
            };
            
            // Restore a minimized GUI
            const restoreMinimizedGui = (id) => {
                const guiToRestore = minimizedGuis.find(gui => gui.id === id);
                
                if (guiToRestore) {
                    // Add back to floating GUIs
                    setFloatingGuis(prev => [...prev, guiToRestore]);
                    
                    // Remove from minimized tray
                    setMinimizedGuis(prev => prev.filter(gui => gui.id !== id));
                }
            };

            // Initialize camera
            const initializeCamera = async () => {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                    setCameraStream(stream);
                    return stream;
                } catch (err) {
                    console.error("Error accessing camera:", err);
                    addMessage('agent', "Sorry, I couldn't access your camera. Please check your permissions.");
                    return null;
                }
            };

            // Dialer functions
            const dialNumber = (num) => {
                setDialerNumber(prev => prev + num);
            };

            const clearDialer = () => {
                setDialerNumber('');
            };

            const makeCall = () => {
                if (dialerNumber) {
                    addMessage('agent', `Initiating call to ${dialerNumber}...`);
                    speak(`Calling ${dialerNumber}`, language);
                    
                    // Simulate call connection
                    setTimeout(() => {
                        addMessage('agent', `Connected to ${dialerNumber}. Call duration will be tracked.`);
                    }, 2000);
                }
            };

            // Configure interact.js on floating GUIs after render
            useEffect(() => {
                floatingGuis.forEach(gui => {
                    const element = document.querySelector(`[data-gui-id="${gui.id}"]`);
                    if (element && !gui.fullscreen) {
                        configureGuiInteractions(element, gui.id);
                    }
                });
            }, [floatingGuis]);

            // Render GUI content
            const renderGuiContent = (gui) => {
                if (gui.url) {
                    return (
                        <iframe 
                            src={gui.url} 
                            className="floating-gui-iframe"
                            title={gui.title}
                        />
                    );
                }

                switch(gui.content) {
                    case 'settings':
                        return (
                            <div className="settings-gui">
                                {/* Settings Options */}
                                <div className="settings-options">
                                    <div className="settings-panel">
                                        <h3>🎨 Appearance</h3>
                                        <label style={{display: 'block', marginBottom: '8px', color: 'var(--text-color)'}}>Theme</label>
                                        <select 
                                            style={{
                                                width: '100%',
                                                background: 'rgba(15, 23, 42, 0.8)',
                                                border: '1px solid rgba(75, 85, 99, 0.5)',
                                                borderRadius: '8px',
                                                padding: '10px 14px',
                                                color: 'white',
                                                fontSize: '14px'
                                            }}
                                            value={theme}
                                            onChange={(e) => setTheme(e.target.value)}
                                        >
                                            <option value="dark">Dark Mode</option>
                                            <option value="light">Light Mode</option>
                                        </select>
                                    </div>
                                    
                                    <div className="settings-panel">
                                        <h3>🗣️ Voice & Language</h3>
                                        <label style={{display: 'block', marginBottom: '8px', color: 'var(--text-color)'}}>Agent Lee's Language</label>
                                        <select 
                                            style={{
                                                width: '100%',
                                                background: 'rgba(15, 23, 42, 0.8)',
                                                border: '1px solid rgba(75, 85, 99, 0.5)',
                                                borderRadius: '8px',
                                                padding: '10px 14px',
                                                color: 'white',
                                                fontSize: '14px'
                                            }}
                                            value={language}
                                            onChange={(e) => setLanguage(e.target.value)}
                                        >
                                            <option value="en-US">English (US)</option>
                                            <option value="en-GB">English (UK)</option>
                                            <option value="es-ES">Spanish</option>
                                            <option value="fr-FR">French</option>
                                            <option value="de-DE">German</option>
                                            <option value="it-IT">Italian</option>
                                            <option value="ja-JP">Japanese</option>
                                            <option value="zh-CN">Chinese (Simplified)</option>
                                        </select>
                                    </div>
                                </div>
                                
                                {/* 3D Brain Visualization - Always Visible - Taking all remaining space */}
                                <div className="llm-center">
                                    <iframe 
                                        src="zjn86qyed4.html" 
                                        className="llm-iframe"
                                        title="3D Brain Visualization"
                                    />
                                </div>
                            </div>
                        );

                    case 'call':
                        return (
                            <div className="interface-container">
                                <div className="dialer-interface">
                                    <h3 style={{color: 'var(--primary-color)', textAlign: 'center', marginBottom: '20px'}}>Communication Module</h3>
                                    
                                    <div className="dialer-display">
                                        {dialerNumber || 'Enter number...'}
                                    </div>
                                    
                                    <div className="dialer-grid">
                                        {['1','2','3','4','5','6','7','8','9','*','0','#'].map(num => (
                                            <button 
                                                key={num} 
                                                className="dialer-btn"
                                                onClick={() => dialNumber(num)}
                                            >
                                                {num}
                                            </button>
                                        ))}
                                    </div>
                                    
                                    <div style={{display: 'flex', gap: '20px', justifyContent: 'center', marginTop: '20px'}}>
                                        <button className="call-btn" onClick={makeCall}>
                                            <i className="fas fa-phone"></i>
                                        </button>
                                        <button className="camera-btn" onClick={clearDialer}>
                                            <i className="fas fa-backspace"></i>
                                        </button>
                                    </div>
                                    
                                    <div style={{marginTop: '20px', textAlign: 'center', color: 'var(--text-color)', fontSize: '12px'}}>
                                        <p>📞 VoIP & Traditional Calling</p>
                                        <p>🔒 Encrypted Communications</p>
                                    </div>
                                </div>
                            </div>
                        );

                    case 'camera':
                        return (
                            <div className="interface-container">
                                <div className="camera-interface">
                                    {cameraStream ? (
                                        <video 
                                            className="camera-stream"
                                            autoPlay 
                                            ref={(video) => {
                                                if (video && cameraStream) {
                                                    video.srcObject = cameraStream;
                                                }
                                            }}
                                        />
                                    ) : (
                                        <div className="camera-stream" style={{display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white'}}>
                                            <div style={{textAlign: 'center'}}>
                                                <i className="fas fa-camera" style={{fontSize: '48px', marginBottom: '20px', color: 'var(--primary-color)'}}></i>
                                                <p>Click to activate camera</p>
                                                <button 
                                                    onClick={initializeCamera}
                                                    style={{
                                                        background: 'var(--primary-color)',
                                                        color: 'white',
                                                        border: 'none',
                                                        borderRadius: '8px',
                                                        padding: '10px 20px',
                                                        marginTop: '10px',
                                                        cursor: 'pointer'
                                                    }}
                                                >
                                                    Activate Camera
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                    
                                    <div className="camera-controls">
                                        <button className="camera-btn">
                                            <i className="fas fa-camera"></i>
                                        </button>
                                        <button className="camera-btn">
                                            <i className="fas fa-video"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        );

                    case 'files':
                        return (
                            <div className="interface-container">
                                <div className="file-explorer">
                                    <h3 style={{color: 'var(--primary-color)', marginBottom: '20px'}}>File Explorer</h3>
                                    <input 
                                        type="file" 
                                        multiple 
                                        style={{
                                            width: '100%',
                                            background: 'rgba(15, 23, 42, 0.8)',
                                            border: '1px solid var(--primary-color)',
                                            borderRadius: '8px',
                                            padding: '12px',
                                            color: 'white',
                                            marginBottom: '20px'
                                        }}
                                        onChange={(e) => {
                                            const files = Array.from(e.target.files);
                                            console.log('Selected files:', files);
                                            if (files.length > 0) {
                                                addMessage('agent', `Selected ${files.length} file(s): ${files.map(f => f.name).join(', ')}`);
                                                speak(`Selected ${files.length} files.`, language);
                                            }
                                        }}
                                    />
                                    <div className="file-item">
                                        <i className="fas fa-folder file-icon"></i>
                                        <span>Documents</span>
                                    </div>
                                    <div className="file-item">
                                        <i className="fas fa-folder file-icon"></i>
                                        <span>Downloads</span>
                                    </div>
                                    <div className="file-item">
                                        <i className="fas fa-folder file-icon"></i>
                                        <span>Pictures</span>
                                    </div>
                                    <div className="file-item">
                                        <i className="fas fa-folder file-icon"></i>
                                        <span>Videos</span>
                                    </div>
                                </div>
                            </div>
                        );

                    default:
                        return <div>Loading...</div>;
                }
            };

            // Icon for minimized GUIs
            const getMinimizedIcon = (type) => {
                switch(type) {
                    case 'agents': return <i className="fas fa-users-cog"></i>;
                    case 'db': return <i className="fas fa-database"></i>;
                    case 'todo': return <i className="fas fa-tasks"></i>;
                    case 'workers': return <i className="fas fa-cogs"></i>;
                    case 'llmcenter': return <i className="fas fa-brain"></i>;
                    case 'settings': return <i className="fas fa-cog"></i>;
                    case 'call': return <i className="fas fa-phone"></i>;
                    case 'sms': return <i className="fas fa-comment"></i>;
                    case 'search': return <i className="fas fa-search"></i>;
                    case 'camera': return <i className="fas fa-camera"></i>;
                    case 'files': return <i className="fas fa-folder"></i>;
                    default: return <i className="fas fa-window-restore"></i>;
                }
            };

            return (
                <div className="app">
                    {/* Floating Agent Lee Card - Always visible unless minimized */}
                    <div className={`agent-card ${agentCardMinimized ? 'minimized' : ''}`} ref={agentCardRef}>
                        <div className="card-header">
                            <img 
                                src="5i4hfqdrs2.png" 
                                alt="Agent Lee" 
                                className="agent-avatar"
                                onClick={handleAvatarClick}
                            />
                            {!agentCardMinimized && (
                                <>
                                    <div className="agent-info">
                                        <h3>Agent Lee</h3>
                                        <p>● Cognitive Systems Online</p>
                                    </div>
                                    <div className="card-controls">
                                        <button 
                                            className={`control-btn ${isTalking ? 'talking' : ''}`}
                                            onClick={toggleTalking}
                                            title={isTalking ? 'Stop Talking' : 'Start Talking'}
                                        >
                                            <i className={`fas ${isTalking ? 'fa-stop' : 'fa-play'}`}></i>
                                        </button>
                                        <button 
                                            className={`control-btn ${isRecording ? 'recording' : ''}`}
                                            onClick={toggleRecording}
                                            title={isRecording ? 'Stop Recording' : 'Voice Search'}
                                        >
                                            <i className={`fas ${isRecording ? 'fa-stop' : 'fa-microphone'}`}></i>
                                        </button>
                                        <button 
                                            className="control-btn minimize"
                                            onClick={minimizeAgentCard}
                                            title="Minimize Agent Card"
                                        >
                                            <i className="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </>
                            )}
                        </div>

                        {!agentCardMinimized && (
                            <div className="card-content">
                                {/* Chat Area */}
                                <div className="chat-area">
                                    {messages.length === 0 ? (
                                        <div className="empty-chat">Start a conversation with Agent Lee...</div>
                                    ) : (
                                        messages.map((message, index) => (
                                            <div key={index} className={`chat-message ${message.type}`}>
                                                {message.content}
                                            </div>
                                        ))
                                    )}
                                </div>

                                {/* Input Area */}
                                <div className="input-area">
                                    <div className="input-wrapper">
                                        <textarea
                                            className="message-input"
                                            placeholder="Type your message to Agent Lee..."
                                            value={inputText}
                                            onChange={(e) => setInputText(e.target.value)}
                                            onKeyPress={handleKeyPress}
                                            rows="2"
                                        />
                                        <button className="send-btn" onClick={handleSendMessage}>
                                            <i className="fas fa-paper-plane"></i>
                                        </button>
                                    </div>
                                </div>

                                {/* Navigation Grid */}
                                <div className="nav-grid">
                                    <button className="nav-btn" onClick={() => toggleUnifiedDashboard()}>
                                        <i className="fas fa-brain icon"></i>
                                        Unified Dashboard
                                    </button>

                                    <button className="nav-btn" onClick={() => window.testAgentLeeAZRBridge()}>
                                        <i className="fas fa-robot icon"></i>
                                        AZR API
                                    </button>

                                    <button className="nav-btn" onClick={() => window.showSystemStatus()}>
                                        <i className="fas fa-chart-line icon"></i>
                                        Status
                                    </button>

                                    <button className="nav-btn" onClick={() => createFloatingGui('search')}>
                                        <i className="fas fa-search icon"></i>
                                        Search
                                    </button>

                                    <button className="nav-btn" onClick={() => createFloatingGui('camera')}>
                                        <i className="fas fa-camera icon"></i>
                                        Camera
                                    </button>

                                    <button className="nav-btn" onClick={() => createFloatingGui('settings')}>
                                        <i className="fas fa-cog icon"></i>
                                        Settings
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                    
                    {/* Floating GUIs */}
                    {floatingGuis.map(gui => (
                        <div 
                            key={gui.id}
                            data-gui-id={gui.id}
                            className={`floating-gui ${gui.fullscreen ? 'fullscreen' : ''}`}
                            style={gui.fullscreen ? {} : {
                                width: `${gui.width}px`,
                                height: `${gui.height}px`,
                                top: `${gui.position.y}px`,
                                left: `${gui.position.x}px`
                            }}
                        >
                            <div className="floating-gui-header">
                                <div className="floating-gui-title">
                                    <i className="fas fa-brain"></i>
                                    {gui.title}
                                </div>
                                <div className="floating-gui-controls">
                                    <button 
                                        className="floating-gui-btn floating-gui-fullscreen"
                                        onClick={() => toggleFullscreen(gui.id)}
                                        title={gui.fullscreen ? "Exit Fullscreen" : "Fullscreen"}
                                    >
                                        <i className={`fas ${gui.fullscreen ? 'fa-compress' : 'fa-expand'}`}></i>
                                    </button>
                                    <button 
                                        className="floating-gui-btn floating-gui-minimize"
                                        onClick={() => minimizeFloatingGui(gui.id)}
                                        title="Minimize"
                                    >
                                        <i className="fas fa-minus"></i>
                                    </button>
                                    <button 
                                        className="floating-gui-btn floating-gui-close"
                                        onClick={() => closeFloatingGui(gui.id)}
                                        title="Close"
                                    >
                                        <i className="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div className="floating-gui-content">
                                {renderGuiContent(gui)}
                            </div>
                            {!gui.fullscreen && <div className="resize-handle"></div>}
                        </div>
                    ))}
                    
                    {/* Minimized GUIs Tray */}
                    <div className="minimized-tray">
                        {minimizedGuis.map(gui => (
                            <div 
                                key={gui.id} 
                                className="minimized-icon"
                                onClick={() => restoreMinimizedGui(gui.id)}
                                title={gui.title}
                            >
                                {getMinimizedIcon(gui.type)}
                            </div>
                        ))}
                    </div>
                </div>
            );
        }

        // ===== INITIALIZATION =====

        // Initialize Agent Lee Cognitive Core System
        const initializeAgentLeeSystem = () => {
            // Load schemas
            loadAgentLeeSchemas();

            // Create activity ticker
            createActivityTicker();

            // Initialize brain visualization
            initializeBrainVisualization();

            // Set up global functions
            window.showPanel = showPanel;
            window.toggleUnifiedDashboard = toggleUnifiedDashboard;
            window.testStructuredOutput = testStructuredOutput;
            window.pulseAllHemispheres = pulseAllHemispheres;
            window.initializeAllSystems = initializeAllSystems;
            window.flashHemisphere = flashHemisphere;
            window.updateActivityTicker = updateActivityTicker;

            // Initial system message
            updateActivityTicker('Agent Lee Cognitive Core: System ready');

            console.log('🧠 Agent Lee Cognitive Core System Initialized');
            console.log('📋 Available Hemispheres:', Object.keys(BRAIN_HEMISPHERES));
            console.log('🎛️ Use toggleUnifiedDashboard() to switch to unified view');
        };

        // Initialize on DOM content loaded
        document.addEventListener('DOMContentLoaded', () => {
            initializeAgentLeeSystem();
        });

        // Render the app using React 18 createRoot
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<AgentLeeApp />);
    </script>
</body>
</html>