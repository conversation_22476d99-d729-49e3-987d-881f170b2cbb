<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Lee - Cognitive Core Interface</title>
    
    <!-- Essential Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;500;700;900&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* Color System */
            --llm-dark-blue: #0a1525;
            --llm-mid-blue: #112240;
            --llm-light-blue: #1e3a5f;
            --llm-accent-blue: #00b4d8;
            --llm-glow-blue: #48cae4;
            --llm-neon-blue: #00eeff;
            --llm-text-color: #e0f7fa;
            --llm-primary: #6C47FF;
            --llm-secondary: #00E3FF;
            --llm-accent: #ff2a6d;
            --llm-success: #1bf7cd;
            --llm-warning: #f7d31b;
            --llm-danger: #f73a1b;
            
            /* UI Components */
            --panel-bg: rgba(16, 20, 40, 0.4);
            --panel-border: rgba(0, 180, 216, 0.3);
            --card-shadow: 0 20px 60px rgba(3, 4, 16, 0.4);
            --card-glow: 0 0 40px rgba(108, 71, 255, 0.2);
            --transition: all 0.4s cubic-bezier(0.17, 0.84, 0.44, 1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, var(--llm-dark-blue), #1a1040, #0f0520);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            color: var(--llm-text-color);
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }
        
        @keyframes gradientBG {
            0% { background-position: 0% 50% }
            50% { background-position: 100% 50% }
            100% { background-position: 0% 50% }
        }
        
        /* Background Effects */
        .background-grid {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 242, 255, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 242, 255, 0.05) 1px, transparent 1px);
            background-size: 40px 40px;
            animation: gridMove 20s linear infinite;
            opacity: 0.3;
            z-index: -1;
        }
        
        @keyframes gridMove {
            0% { background-position: 0 0; }
            100% { background-position: 40px 40px; }
        }
        
        /* Main Container */
        .container {
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            grid-template-rows: auto 1fr;
            height: 100vh;
            gap: 15px;
            padding: 15px;
        }
        
        /* Header */
        .header {
            grid-column: 1 / -1;
            background: linear-gradient(to right, rgba(10, 14, 41, 0.8), rgba(30, 15, 70, 0.8));
            border: 1px solid var(--panel-border);
            border-radius: 12px;
            padding: 20px 25px;
            backdrop-filter: blur(10px);
            box-shadow: var(--card-glow);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--llm-primary), var(--llm-secondary));
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            box-shadow: 0 10px 25px rgba(108, 71, 255, 0.4);
            animation: pulse 2s infinite;
        }
        
        .brand-name {
            font-family: 'Orbitron', sans-serif;
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(to right, var(--llm-accent-blue), var(--llm-neon-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 15px rgba(0, 180, 216, 0.7);
        }
        
        .system-status {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(16, 20, 40, 0.6);
            padding: 10px 20px;
            border-radius: 25px;
            border: 1px solid var(--panel-border);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--llm-success);
            box-shadow: 0 0 10px var(--llm-success);
            animation: pulse 2s infinite;
        }
        
        .azr-status {
            font-family: 'Orbitron', sans-serif;
            font-size: 14px;
            color: var(--llm-accent-blue);
        }
        
        /* Left Panel - System Controls */
        .left-panel {
            background: var(--panel-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--panel-border);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
            box-shadow: var(--card-shadow);
            overflow-y: auto;
        }
        
        .panel-section {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid rgba(108, 71, 255, 0.1);
        }
        
        .panel-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 14px;
            font-weight: 600;
            color: var(--llm-accent-blue);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-button {
            width: 100%;
            background: linear-gradient(135deg, var(--llm-primary), var(--llm-secondary));
            border: none;
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }
        
        .control-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(108, 71, 255, 0.3);
        }
        
        .activity-log {
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.4;
        }
        
        .log-entry {
            padding: 6px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            gap: 10px;
        }
        
        .log-time {
            color: var(--llm-accent-blue);
            white-space: nowrap;
        }
        
        .log-message {
            color: var(--llm-text-color);
        }
        
        /* Center Panel - 3D Brain */
        .center-panel {
            background: var(--panel-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--panel-border);
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            display: flex;
            flex-direction: column;
        }
        
        .brain-header {
            padding: 15px 20px;
            background: rgba(0, 0, 0, 0.2);
            border-bottom: 1px solid var(--panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .brain-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 16px;
            font-weight: 600;
            color: var(--llm-accent-blue);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .brain-controls {
            display: flex;
            gap: 10px;
        }
        
        .brain-control-btn {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid var(--panel-border);
            color: var(--llm-text-color);
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: var(--transition);
        }
        
        .brain-control-btn:hover {
            background: rgba(108, 71, 255, 0.2);
            border-color: var(--llm-primary);
        }
        
        .brain-container {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, rgba(0, 0, 0, 0.2), rgba(0, 242, 255, 0.02));
            min-height: 400px;
        }
        
        #brain-canvas {
            width: 100%;
            height: 100%;
            cursor: grab;
        }
        
        #brain-canvas:active {
            cursor: grabbing;
        }
        
        .brain-labels {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        
        .brain-label {
            position: absolute;
            background: rgba(10, 21, 37, 0.9);
            color: var(--llm-text-color);
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
            border: 1px solid var(--llm-accent-blue);
            box-shadow: 0 0 15px rgba(0, 180, 216, 0.4);
            transform: translate(-50%, -50%);
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s;
            pointer-events: auto;
            cursor: pointer;
        }
        
        .brain-label.visible {
            opacity: 1;
        }
        
        .brain-label:hover {
            background: rgba(0, 180, 216, 0.2);
            box-shadow: 0 0 20px rgba(0, 180, 216, 0.6);
        }
        
        .brain-info {
            position: absolute;
            bottom: 15px;
            left: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--panel-border);
            font-size: 12px;
            line-height: 1.4;
        }
        
        /* Right Panel - System Metrics */
        .right-panel {
            background: var(--panel-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--panel-border);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
            box-shadow: var(--card-shadow);
            overflow-y: auto;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .metric-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid rgba(108, 71, 255, 0.1);
            text-align: center;
        }
        
        .metric-value {
            font-family: 'Orbitron', sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: var(--llm-accent-blue);
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 12px;
            color: rgba(224, 231, 255, 0.7);
        }
        
        .chart-container {
            height: 150px;
            position: relative;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 10px;
        }
        
        /* Region Modal System */
        .region-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .region-modal.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal-content {
            width: 95%;
            height: 95%;
            max-width: 1400px;
            background: var(--panel-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--panel-border);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), var(--card-glow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .modal-header {
            padding: 20px 25px;
            background: rgba(24, 26, 44, 0.8);
            border-bottom: 1px solid var(--panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 20px;
            font-weight: 600;
            color: var(--llm-accent-blue);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .modal-close {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid var(--panel-border);
            color: var(--llm-accent);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
            font-size: 16px;
        }
        
        .modal-close:hover {
            background: rgba(255, 42, 109, 0.2);
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(255, 42, 109, 0.4);
        }
        
        .modal-body {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .embedded-content {
            flex: 1;
            overflow: auto;
            background: transparent;
        }
        
        /* Animations */
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(0.95); }
        }
        
        /* Responsive Design */
        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto 1fr auto;
            }
            
            .left-panel, .right-panel {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
                gap: 10px;
            }
            
            .left-panel, .right-panel {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Background Grid -->
    <div class="background-grid"></div>
    
    <!-- Main Container -->
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo-section">
                <div class="logo-icon">🧠</div>
                <div class="brand-name">Agent Lee - Cognitive Core</div>
            </div>
            <div class="system-status">
                <div class="status-indicator"></div>
                <div class="azr-status">AZR Cycle: READY</div>
            </div>
        </header>
        
        <!-- Left Panel - System Controls -->
        <div class="left-panel">
            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-cogs"></i> System Controls
                </div>
                <button class="control-button" onclick="showAllRegions()">
                    <i class="fas fa-eye"></i> View All Regions
                </button>
                <button class="control-button" onclick="runSystemDiagnostic()">
                    <i class="fas fa-stethoscope"></i> Run Diagnostics
                </button>
                <button class="control-button" onclick="syncDatabase()">
                    <i class="fas fa-sync"></i> Sync Database
                </button>
                <button class="control-button" onclick="toggleAZRMode()">
                    <i class="fas fa-recycle"></i> Toggle AZR Mode
                </button>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-history"></i> Activity Log
                </div>
                <div class="activity-log" id="activityLog">
                    <div class="log-entry">
                        <div class="log-time">14:32:15</div>
                        <div class="log-message">System initialized successfully</div>
                    </div>
                    <div class="log-entry">
                        <div class="log-time">14:32:16</div>
                        <div class="log-message">3D Brain visualization loaded</div>
                    </div>
                    <div class="log-entry">
                        <div class="log-time">14:32:17</div>
                        <div class="log-message">All neural connections established</div>
                    </div>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-brain"></i> Brain Regions
                </div>
                <button class="control-button" onclick="openRegionDirect('datacore')">
                    <i class="fas fa-database"></i> Data Core
                </button>
                <button class="control-button" onclick="openRegionDirect('todo')">
                    <i class="fas fa-tasks"></i> To-Do Nexus
                </button>
                <button class="control-button" onclick="openRegionDirect('agents')">
                    <i class="fas fa-users-cog"></i> Agent Center
                </button>
                <button class="control-button" onclick="openRegionDirect('workers')">
                    <i class="fas fa-cogs"></i> Worker Grid
                </button>
            </div>
        </div>
        
        <!-- Center Panel - 3D Brain -->
        <div class="center-panel">
            <div class="brain-header">
                <div class="brain-title">
                    <i class="fas fa-brain"></i> Neural Network Visualization
                </div>
                <div class="brain-controls">
                    <button class="brain-control-btn" onclick="resetBrainView()">
                        <i class="fas fa-home"></i> Reset View
                    </button>
                    <button class="brain-control-btn" onclick="autoRotateBrain()">
                        <i class="fas fa-sync-alt"></i> Auto Rotate
                    </button>
                </div>
            </div>
            
            <div class="brain-container">
                <canvas id="brain-canvas"></canvas>
                <div class="brain-labels" id="brainLabels"></div>
                
                <div class="brain-info">
                    <strong>🧠 Interactive Brain Map</strong><br>
                    <strong>Click and drag</strong> to rotate the brain view<br>
                    <strong>Scroll</strong> to zoom in/out<br>
                    <strong>Click regions</strong> to access detailed interfaces<br>
                    <strong>Regions:</strong> Data Core (purple), To-Do Nexus (teal), Agent Center (red), Worker Grid (yellow)
                </div>
            </div>
        </div>
        
        <!-- Right Panel - System Metrics -->
        <div class="right-panel">
            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-chart-line"></i> System Metrics
                </div>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="activeAgents">110</div>
                        <div class="metric-label">Active Agents</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="activeWorkers">255</div>
                        <div class="metric-label">Active Workers</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="taskQueue">47</div>
                        <div class="metric-label">Tasks in Queue</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="memoryUsage">94%</div>
                        <div class="metric-label">Memory Usage</div>
                    </div>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-heartbeat"></i> System Health
                </div>
                <div class="chart-container">
                    <canvas id="healthChart"></canvas>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">
                    <i class="fas fa-network-wired"></i> Network Activity
                </div>
                <div class="chart-container">
                    <canvas id="networkChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Region Modals -->
    <div class="region-modal" id="datacore-modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-database"></i> Data Core & Database Center
                </div>
                <div class="modal-close" onclick="closeRegionModal('datacore-modal')">
                    <i class="fas fa-times"></i>
                </div>
            </div>
            <div class="modal-body">
                <div class="embedded-content" id="datacore-content">Loading...</div>
            </div>
        </div>
    </div>
    
    <div class="region-modal" id="todo-modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-tasks"></i> Operations & To-Do Nexus
                </div>
                <div class="modal-close" onclick="closeRegionModal('todo-modal')">
                    <i class="fas fa-times"></i>
                </div>
            </div>
            <div class="modal-body">
                <div class="embedded-content" id="todo-content">Loading...</div>
            </div>
        </div>
    </div>
    
    <div class="region-modal" id="agents-modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-users-cog"></i> Agent Orchestration Center
                </div>
                <div class="modal-close" onclick="closeRegionModal('agents-modal')">
                    <i class="fas fa-times"></i>
                </div>
            </div>
            <div class="modal-body">
                <div class="embedded-content" id="agents-content">Loading...</div>
            </div>
        </div>
    </div>
    
    <div class="region-modal" id="workers-modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <i class="fas fa-cogs"></i> Integrated Worker Grid
                </div>
                <div class="modal-close" onclick="closeRegionModal('workers-modal')">
                    <i class="fas fa-times"></i>
                </div>
            </div>
            <div class="modal-body">
                <div class="embedded-content" id="workers-content">Loading...</div>
            </div>
        </div>
    </div>

    <script>
        // Global State
        let scene, camera, renderer, brainGroup;
        let raycaster, mouse;
        let brainRegions = [];
        let activeRegion = null;
        let charts = {};
        let isAZRActive = false;
        let autoRotate = false;
        let isDragging = false;
        let previousMousePosition = { x: 0, y: 0 };
        
        // Brain Regions Configuration - Updated with all regions
        const regionConfig = [
            {
                name: 'DataCore',
                label: 'DATA CORE',
                position: { x: 2.5, y: 1.2, z: 0.8 },
                color: 0x6C47FF,
                modalId: 'datacore-modal',
                contentFile: '6wg2917how.html'
            },
            {
                name: 'OperationsToDoNexus',
                label: 'TO-DO NEXUS',
                position: { x: -2.5, y: 1.2, z: 0.8 },
                color: 0x1bf7cd,
                modalId: 'todo-modal',
                contentFile: 'v6m0y2alws.html'
            },
            {
                name: 'AgentOrchestration',
                label: 'AGENT CENTER',
                position: { x: -2.5, y: -1.2, z: 0.8 },
                color: 0xff2a6d,
                modalId: 'agents-modal',
                contentFile: '0pqp9eakre.html'
            },
            {
                name: 'WorkerGrid',
                label: 'WORKER GRID',
                position: { x: 2.5, y: -1.2, z: 0.8 },
                color: 0xf7d31b,
                modalId: 'workers-modal',
                contentFile: '23qhtmokbq.html'
            },
            {
                name: 'LLMBrainCenter',
                label: 'LLM BRAIN CENTER',
                position: { x: 0, y: 2.5, z: 0 },
                color: 0x00f2ff,
                modalId: 'llm-modal',
                contentFile: 'px51uuyzex.html'
            }
        ];
        
        // Initialize System
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧠 Initializing Agent Lee Cognitive Core...');
            
            // Initialize 3D Brain
            init3DBrain();
            
            // Initialize Charts
            initializeCharts();
            
            // Start Activity Simulation
            startActivitySimulation();
            
            // Update system time
            updateSystemTime();
            setInterval(updateSystemTime, 1000);
            
            console.log('✅ Agent Lee Cognitive Core Online');
        });
        
        // 3D Brain Initialization
        function init3DBrain() {
            const canvas = document.getElementById('brain-canvas');
            const container = canvas.parentElement;
            
            // Scene setup
            scene = new THREE.Scene();
            scene.background = null; // Transparent background
            
            // Camera setup with better positioning
            camera = new THREE.PerspectiveCamera(60, container.clientWidth / container.clientHeight, 0.1, 1000);
            camera.position.set(0, 0, 12);
            
            // Renderer setup
            renderer = new THREE.WebGLRenderer({ 
                canvas: canvas, 
                antialias: true, 
                alpha: true,
                powerPreference: "high-performance"
            });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
            
            // Enhanced lighting
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0x48cae4, 1.2);
            directionalLight.position.set(5, 5, 5);
            scene.add(directionalLight);
            
            const pointLight1 = new THREE.PointLight(0x6C47FF, 0.8);
            pointLight1.position.set(-5, -5, 3);
            scene.add(pointLight1);
            
            const pointLight2 = new THREE.PointLight(0xff2a6d, 0.6);
            pointLight2.position.set(5, -5, -3);
            scene.add(pointLight2);
            
            // Brain Group
            brainGroup = new THREE.Group();
            scene.add(brainGroup);
            
            // Create brain structure
            createBrainStructure();
            
            // Interaction setup
            raycaster = new THREE.Raycaster();
            mouse = new THREE.Vector2();
            
            // Enhanced interaction handlers
            canvas.addEventListener('mousedown', onMouseDown);
            canvas.addEventListener('mousemove', onMouseMove);
            canvas.addEventListener('mouseup', onMouseUp);
            canvas.addEventListener('wheel', onMouseWheel);
            canvas.addEventListener('click', onBrainClick);
            window.addEventListener('resize', onWindowResize);
            
            // Animation loop
            animate();
        }
        
        // Enhanced Brain Structure
        function createBrainStructure() {
            // Central brain cortex with better geometry
            const cortexGeometry = new THREE.SphereGeometry(3, 128, 128);
            
            // Enhanced noise pattern for more realistic brain surface
            const positions = cortexGeometry.attributes.position;
            for (let i = 0; i < positions.count; i++) {
                const vertex = new THREE.Vector3();
                vertex.fromBufferAttribute(positions, i);
                
                // Multiple layers of noise for complex brain surface
                const noise1 = Math.sin(vertex.x * 6) * Math.cos(vertex.y * 6) * Math.sin(vertex.z * 6) * 0.08;
                const noise2 = Math.sin(vertex.x * 12) * Math.cos(vertex.y * 12) * Math.sin(vertex.z * 12) * 0.04;
                const noise3 = Math.sin(vertex.x * 24) * Math.cos(vertex.y * 24) * Math.sin(vertex.z * 24) * 0.02;
                
                vertex.multiplyScalar(1 + noise1 + noise2 + noise3);
                positions.setXYZ(i, vertex.x, vertex.y, vertex.z);
            }
            cortexGeometry.attributes.position.needsUpdate = true;
            cortexGeometry.computeVertexNormals();
            
            // Enhanced brain material
            const cortexMaterial = new THREE.MeshPhongMaterial({
                color: 0x4a9eff,
                emissive: 0x1a3d5f,
                emissiveIntensity: 0.15,
                transparent: true,
                opacity: 0.7,
                shininess: 30,
                wireframe: false
            });
            
            const cortex = new THREE.Mesh(cortexGeometry, cortexMaterial);
            brainGroup.add(cortex);
            
            // Create enhanced brain regions
            regionConfig.forEach(region => {
                const regionGeometry = new THREE.SphereGeometry(0.5, 32, 32);
                const regionMaterial = new THREE.MeshPhongMaterial({
                    color: region.color,
                    emissive: region.color,
                    emissiveIntensity: 0.4,
                    transparent: true,
                    opacity: 0.9,
                    shininess: 100
                });
                
                const regionMesh = new THREE.Mesh(regionGeometry, regionMaterial);
                regionMesh.position.set(region.position.x, region.position.y, region.position.z);
                regionMesh.userData = { region: region };
                
                // Add glow effect
                const glowGeometry = new THREE.SphereGeometry(0.6, 16, 16);
                const glowMaterial = new THREE.MeshBasicMaterial({
                    color: region.color,
                    transparent: true,
                    opacity: 0.2
                });
                const glow = new THREE.Mesh(glowGeometry, glowMaterial);
                regionMesh.add(glow);
                
                brainGroup.add(regionMesh);
                brainRegions.push(regionMesh);
                
                // Create label
                createRegionLabel(region);
            });
            
            // Create enhanced neural connections
            createNeuralConnections();
        }
        
        // Enhanced Region Labels
        function createRegionLabel(region) {
            const label = document.createElement('div');
            label.className = 'brain-label';
            label.textContent = region.label;
            label.id = `label-${region.name}`;
            label.style.borderColor = `#${region.color.toString(16).padStart(6, '0')}`;
            label.onclick = () => openRegionModal(region);
            
            document.getElementById('brainLabels').appendChild(label);
        }
        
        // Enhanced Label Updates
        function updateRegionLabels() {
            if (!camera || !brainGroup) return;
            
            const canvas = document.getElementById('brain-canvas');
            const canvasRect = canvas.getBoundingClientRect();
            
            brainRegions.forEach(regionMesh => {
                const region = regionMesh.userData.region;
                const label = document.getElementById(`label-${region.name}`);
                if (!label) return;
                
                const tempV = new THREE.Vector3();
                regionMesh.updateWorldMatrix(true, false);
                tempV.setFromMatrixPosition(regionMesh.matrixWorld);
                tempV.project(camera);
                
                // Check if region is in front of camera
                if (tempV.z > 1 || tempV.z < -1) {
                    label.classList.remove('visible');
                } else {
                    const x = (tempV.x * 0.5 + 0.5) * canvasRect.width;
                    const y = (tempV.y * -0.5 + 0.5) * canvasRect.height;
                    
                    // Keep labels within canvas bounds
                    const labelX = Math.max(50, Math.min(canvasRect.width - 50, x));
                    const labelY = Math.max(20, Math.min(canvasRect.height - 20, y));
                    
                    label.style.left = `${labelX}px`;
                    label.style.top = `${labelY}px`;
                    label.classList.add('visible');
                }
            });
        }
        
        // Enhanced Neural Connections
        function createNeuralConnections() {
            const connectionMaterial = new THREE.LineBasicMaterial({
                color: 0x40e0d0,
                transparent: true,
                opacity: 0.3,
                linewidth: 2
            });
            
            // Connect regions to center with curved lines
            regionConfig.forEach((region, index) => {
                // Create curved connection to center
                const curve = new THREE.QuadraticBezierCurve3(
                    new THREE.Vector3(0, 0, 0),
                    new THREE.Vector3(
                        region.position.x * 0.5,
                        region.position.y * 0.5,
                        region.position.z + 1
                    ),
                    new THREE.Vector3(region.position.x, region.position.y, region.position.z)
                );
                
                const points = curve.getPoints(20);
                const geometry = new THREE.BufferGeometry().setFromPoints(points);
                const line = new THREE.Line(geometry, connectionMaterial.clone());
                line.material.opacity = 0.4 + Math.random() * 0.3;
                brainGroup.add(line);
            });
            
            // Connect regions to each other with subtle connections
            for (let i = 0; i < regionConfig.length; i++) {
                for (let j = i + 1; j < regionConfig.length; j++) {
                    if (Math.random() > 0.7) { // Only some connections
                        const points = [];
                        points.push(new THREE.Vector3(
                            regionConfig[i].position.x,
                            regionConfig[i].position.y,
                            regionConfig[i].position.z
                        ));
                        points.push(new THREE.Vector3(
                            regionConfig[j].position.x,
                            regionConfig[j].position.y,
                            regionConfig[j].position.z
                        ));
                        
                        const geometry = new THREE.BufferGeometry().setFromPoints(points);
                        const line = new THREE.Line(geometry, connectionMaterial.clone());
                        line.material.opacity = 0.1;
                        brainGroup.add(line);
                    }
                }
            }
        }
        
        // Enhanced Mouse Interaction
        function onMouseDown(event) {
            isDragging = true;
            previousMousePosition = {
                x: event.clientX,
                y: event.clientY
            };
            document.getElementById('brain-canvas').style.cursor = 'grabbing';
        }
        
        function onMouseMove(event) {
            if (isDragging) {
                const deltaMove = {
                    x: event.clientX - previousMousePosition.x,
                    y: event.clientY - previousMousePosition.y
                };
                
                // Rotate brain group
                brainGroup.rotation.y += deltaMove.x * 0.01;
                brainGroup.rotation.x += deltaMove.y * 0.01;
                
                // Limit rotation on x-axis
                brainGroup.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, brainGroup.rotation.x));
                
                previousMousePosition = {
                    x: event.clientX,
                    y: event.clientY
                };
            }
        }
        
        function onMouseUp(event) {
            isDragging = false;
            document.getElementById('brain-canvas').style.cursor = 'grab';
        }
        
        function onMouseWheel(event) {
            event.preventDefault();
            const zoomSpeed = 0.1;
            const newZ = camera.position.z + (event.deltaY > 0 ? zoomSpeed : -zoomSpeed);
            camera.position.z = Math.max(6, Math.min(25, newZ));
        }
        
        // Enhanced Brain Click Handler
        function onBrainClick(event) {
            if (isDragging) return; // Don't trigger click if we were dragging
            
            event.preventDefault();
            
            const canvas = document.getElementById('brain-canvas');
            const rect = canvas.getBoundingClientRect();
            
            mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
            mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
            
            raycaster.setFromCamera(mouse, camera);
            const intersects = raycaster.intersectObjects(brainRegions);
            
            if (intersects.length > 0) {
                const clickedRegion = intersects[0].object.userData.region;
                openRegionModal(clickedRegion);
                highlightRegion(intersects[0].object);
                logActivity(`Opened ${clickedRegion.label} interface`);
            }
        }
        
        // Enhanced Region Highlighting
        function highlightRegion(regionMesh) {
            // Reset all regions
            brainRegions.forEach(mesh => {
                anime({
                    targets: mesh.material,
                    emissiveIntensity: 0.4,
                    duration: 300
                });
                anime({
                    targets: mesh.scale,
                    x: 1,
                    y: 1,
                    z: 1,
                    duration: 300
                });
            });
            
            // Highlight selected region
            anime({
                targets: regionMesh.material,
                emissiveIntensity: 1.0,
                duration: 300
            });
            anime({
                targets: regionMesh.scale,
                x: 1.3,
                y: 1.3,
                z: 1.3,
                duration: 500,
                easing: 'easeOutElastic(1, .6)'
            });
        }
        
        // Enhanced Modal System
        function openRegionModal(region) {
            const modal = document.getElementById(region.modalId);
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
                
                // Load content if not already loaded
                const contentId = region.name.toLowerCase().replace('orchestration', '').replace('operations', '') + '-content';
                const content = document.getElementById(contentId);
                if (content && content.innerHTML.trim() === 'Loading...') {
                    loadRegionContentIntoModal(region, content);
                }
            }
        }
        
        // Direct region opening function
        function openRegionDirect(regionName) {
            const regionMap = {
                'datacore': 'DataCore',
                'todo': 'OperationsToDoNexus',
                'agents': 'AgentOrchestration',
                'workers': 'WorkerGrid',
                'llm': 'LLMBrainCenter'
            };
            
            const fullRegionName = regionMap[regionName];
            const region = regionConfig.find(r => r.name === fullRegionName);
            
            if (region) {
                openRegionModal(region);
                logActivity(`Opened ${region.label} interface via direct access`);
            }
        }
        
        // Close Region Modal
        function closeRegionModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = 'auto';
            }
        }
        
        // Enhanced Content Loading
        function loadRegionContentIntoModal(region, contentElement) {
            // Load the actual HTML content from the files
            fetch(region.contentFile)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Failed to load ${region.contentFile}`);
                    }
                    return response.text();
                })
                .then(html => {
                    // Extract body content from the HTML
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    
                    // Extract styles and add them to the modal
                    const styles = doc.querySelectorAll('style, link[rel="stylesheet"]');
                    styles.forEach(style => {
                        if (!document.head.querySelector(`[data-region="${region.name}"]`)) {
                            const newStyle = style.cloneNode(true);
                            newStyle.setAttribute('data-region', region.name);
                            document.head.appendChild(newStyle);
                        }
                    });
                    
                    // Extract scripts and add them
                    const scripts = doc.querySelectorAll('script');
                    scripts.forEach(script => {
                        if (script.src) {
                            // External script
                            if (!document.head.querySelector(`script[src="${script.src}"]`)) {
                                const newScript = document.createElement('script');
                                newScript.src = script.src;
                                newScript.setAttribute('data-region', region.name);
                                document.head.appendChild(newScript);
                            }
                        } else {
                            // Inline script
                            const newScript = document.createElement('script');
                            newScript.textContent = script.textContent;
                            newScript.setAttribute('data-region', region.name);
                            contentElement.appendChild(newScript);
                        }
                    });
                    
                    // Set the body content
                    contentElement.innerHTML = doc.body.innerHTML;
                })
                .catch(error => {
                    console.error('Error loading region content:', error);
                    // Fallback to simplified content
                    switch(region.name) {
                        case 'DataCore':
                            contentElement.innerHTML = createDataCoreInterface();
                            break;
                        case 'OperationsToDoNexus':
                            contentElement.innerHTML = createToDoInterface();
                            break;
                        case 'AgentOrchestration':
                            contentElement.innerHTML = createAgentInterface();
                            break;
                        case 'WorkerGrid':
                            contentElement.innerHTML = createWorkerInterface();
                            break;
                        case 'LLMBrainCenter':
                            contentElement.innerHTML = createLLMInterface();
                            break;
                        default:
                            contentElement.innerHTML = `<div style="padding: 20px; text-align: center;">Content for ${region.label} is loading...</div>`;
                    }
                });
        }
        
        // Fallback content creators (simplified versions)
        function createDataCoreInterface() {
            return `
                <div style="padding: 20px; height: 100%; background: linear-gradient(135deg, rgba(108, 71, 255, 0.1), rgba(0, 30, 60, 0.9)); overflow-y: auto;">
                    <h2 style="color: #6C47FF; margin-bottom: 20px;">AgentLee's Database - Memory Core</h2>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                        <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px;">
                            <h4 style="color: #00E3FF;">Memory Health</h4>
                            <div style="font-size: 24px; color: #1bf7cd;">97.6%</div>
                            <div style="font-size: 12px; color: #ccc;">No Memory Leakage Detected</div>
                        </div>
                        <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px;">
                            <h4 style="color: #00E3FF;">Active Connections</h4>
                            <div style="font-size: 24px; color: #1bf7cd;">5/5</div>
                            <div style="font-size: 12px; color: #ccc;">All Systems Connected</div>
                        </div>
                    </div>
                    <div style="background: rgba(0,0,0,0.2); padding: 20px; border-radius: 8px;">
                        <h4 style="color: #00E3FF;">Database Status</h4>
                        <p style="color: #ccc; margin-top: 10px;">All database clusters are operational and synchronized. Memory leakage prevention is active.</p>
                    </div>
                </div>
            `;
        }
        
        function createToDoInterface() {
            return `
                <div style="padding: 20px; height: 100%; background: linear-gradient(135deg, rgba(27, 247, 205, 0.1), rgba(0, 30, 60, 0.9)); overflow-y: auto;">
                    <h2 style="color: #1bf7cd; margin-bottom: 20px;">Agent Lee's Dynamic To-Do List</h2>
                    <div style="margin-bottom: 20px;">
                        <input type="text" placeholder="Add new task..." style="flex: 1; padding: 12px; background: rgba(0,0,0,0.3); border: 1px solid rgba(27,247,205,0.3); border-radius: 6px; color: white; width: 70%;">
                        <button style="padding: 12px 20px; background: linear-gradient(135deg, #1bf7cd, #00E3FF); border: none; border-radius: 6px; color: #0a1525; margin-left: 10px;">Add Task</button>
                    </div>
                    <div style="background: rgba(0,0,0,0.2); padding: 20px; border-radius: 8px;">
                        <h4 style="color: #00E3FF;">Active Tasks</h4>
                        <div style="margin-top: 15px;">
                            <div style="padding: 10px; background: rgba(247,58,27,0.1); border-left: 3px solid #f73a1b; margin: 5px 0;">
                                ⚠️ High Priority: Diagnose LLM neural pathway disruption
                            </div>
                            <div style="padding: 10px; background: rgba(247,211,27,0.1); border-left: 3px solid #f7d31b; margin: 5px 0;">
                                🔶 Medium: Verify agent plan integrity across all nodes
                            </div>
                            <div style="padding: 10px; background: rgba(27,247,205,0.1); border-left: 3px solid #1bf7cd; margin: 5px 0;">
                                ✅ Low: Cache vector store in worker memory
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function createAgentInterface() {
            return `
                <div style="padding: 20px; height: 100%; background: linear-gradient(135deg, rgba(255, 42, 109, 0.1), rgba(0, 30, 60, 0.9)); overflow-y: auto;">
                    <h2 style="color: #ff2a6d; margin-bottom: 20px;">Agent Lee's Agent Center</h2>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 30px;">
                        <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="color: #00E3FF;">Active Agents</h4>
                            <div style="font-size: 24px; color: #ff2a6d;">110</div>
                        </div>
                        <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="color: #00E3FF;">Tasks Completed</h4>
                            <div style="font-size: 24px; color: #ff2a6d;">8,547</div>
                        </div>
                        <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="color: #00E3FF;">System Load</h4>
                            <div style="font-size: 24px; color: #ff2a6d;">73%</div>
                        </div>
                    </div>
                    <div style="background: rgba(0,0,0,0.2); padding: 20px; border-radius: 8px;">
                        <h4 style="color: #00E3FF;">Agent Profiles</h4>
                        <div style="margin-top: 15px;">
                            <div style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; margin: 10px 0;">
                                <strong style="color: #ff2a6d;">Lily (NEXUS-AGENT-001)</strong><br>
                                Function: context_weaver()<br>
                                <small style="color: #ccc;">Core Cognitive & Orchestration</small>
                            </div>
                            <div style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; margin: 10px 0;">
                                <strong style="color: #ff2a6d;">Gabriel (NEXUS-AGENT-002)</strong><br>
                                Function: spec_enforcer()<br>
                                <small style="color: #ccc;">Core Cognitive & Orchestration</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function createWorkerInterface() {
            return `
                <div style="padding: 20px; height: 100%; background: linear-gradient(135deg, rgba(247, 211, 27, 0.1), rgba(0, 30, 60, 0.9)); overflow-y: auto;">
                    <h2 style="color: #f7d31b; margin-bottom: 20px;">Agent Lee's Integrated Workers Center</h2>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 30px;">
                        <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="color: #00E3FF; font-size: 12px;">Total Workers</h4>
                            <div style="font-size: 24px; color: #f7d31b;">255</div>
                        </div>
                        <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="color: #00E3FF; font-size: 12px;">Web Workers</h4>
                            <div style="font-size: 24px; color: #f7d31b;">200</div>
                        </div>
                        <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="color: #00E3FF; font-size: 12px;">Service Workers</h4>
                            <div style="font-size: 24px; color: #f7d31b;">40</div>
                        </div>
                        <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; text-align: center;">
                            <h4 style="color: #00E3FF; font-size: 12px;">Supervisors</h4>
                            <div style="font-size: 24px; color: #f7d31b;">15</div>
                        </div>
                    </div>
                    <div style="background: rgba(0,0,0,0.2); padding: 20px; border-radius: 8px;">
                        <h4 style="color: #00E3FF;">Worker Status</h4>
                        <div style="margin-top: 15px; font-family: monospace; font-size: 12px;">
                            <div>🟢 All workers operational</div>
                            <div>🟡 3 workers at high load</div>
                            <div>🔵 Memory usage: 78MB avg</div>
                            <div>🟢 No failed tasks detected</div>
                            <div>🟢 Response time: 84ms avg</div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function createLLMInterface() {
            return `
                <div style="padding: 20px; height: 100%; background: linear-gradient(135deg, rgba(0, 242, 255, 0.1), rgba(0, 30, 60, 0.9)); overflow-y: auto;">
                    <h2 style="color: #00f2ff; margin-bottom: 20px;">LLM Brain Center</h2>
                    <div style="background: rgba(0,0,0,0.2); padding: 20px; border-radius: 8px;">
                        <h4 style="color: #00E3FF;">Neural Network Control</h4>
                        <p style="color: #ccc; margin-top: 10px;">Central hub for LLM operations and neural pathway management.</p>
                        <div style="margin-top: 20px;">
                            <div style="padding: 10px; background: rgba(0,242,255,0.1); border-radius: 5px; margin: 5px 0;">
                                🧠 Primary LLM: Online and Processing
                            </div>
                            <div style="padding: 10px; background: rgba(0,242,255,0.1); border-radius: 5px; margin: 5px 0;">
                                ⚡ Neural Pathways: All Active
                            </div>
                            <div style="padding: 10px; background: rgba(0,242,255,0.1); border-radius: 5px; margin: 5px 0;">
                                🔗 Connections: Stable
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // Initialize Charts
        function initializeCharts() {
            initHealthChart();
            initNetworkChart();
        }
        
        function initHealthChart() {
            const ctx = document.getElementById('healthChart').getContext('2d');
            charts.health = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 20}, (_, i) => i),
                    datasets: [{
                        label: 'System Health',
                        data: Array.from({length: 20}, () => Math.random() * 20 + 80),
                        borderColor: '#1bf7cd',
                        backgroundColor: 'rgba(27, 247, 205, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { 
                            display: false,
                            min: 0,
                            max: 100
                        }
                    },
                    elements: { point: { radius: 0 } }
                }
            });
        }
        
        function initNetworkChart() {
            const ctx = document.getElementById('networkChart').getContext('2d');
            charts.network = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Agents', 'Workers', 'Database', 'LLM'],
                    datasets: [{
                        data: [110, 255, 5, 1],
                        backgroundColor: [
                            '#ff2a6d',
                            '#f7d31b',
                            '#6C47FF',
                            '#1bf7cd'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#e0f7fa',
                                font: { size: 10 }
                            }
                        }
                    }
                }
            });
        }
        
        // Animation Loop
        function animate() {
            requestAnimationFrame(animate);
            
            if (brainGroup) {
                if (autoRotate) {
                    brainGroup.rotation.y += 0.005;
                }
                updateRegionLabels();
            }
            
            renderer.render(scene, camera);
        }
        
        // Brain Control Functions
        function resetBrainView() {
            anime({
                targets: brainGroup.rotation,
                x: 0,
                y: 0,
                z: 0,
                duration: 1000,
                easing: 'easeInOutQuad'
            });
            
            anime({
                targets: camera.position,
                z: 12,
                duration: 1000,
                easing: 'easeInOutQuad'
            });
            
            logActivity('Brain view reset to default position');
        }
        
        function autoRotateBrain() {
            autoRotate = !autoRotate;
            const btn = event.target;
            if (autoRotate) {
                btn.innerHTML = '<i class="fas fa-pause"></i> Stop Rotate';
                logActivity('Auto-rotation enabled');
            } else {
                btn.innerHTML = '<i class="fas fa-sync-alt"></i> Auto Rotate';
                logActivity('Auto-rotation disabled');
            }
        }
        
        // Window Resize Handler
        function onWindowResize() {
            const canvas = document.getElementById('brain-canvas');
            const container = canvas.parentElement;
            
            camera.aspect = container.clientWidth / container.clientHeight;
            camera.updateProjectionMatrix();
            
            renderer.setSize(container.clientWidth, container.clientHeight);
        }
        
        // Utility Functions
        function logActivity(message) {
            const log = document.getElementById('activityLog');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            
            const time = document.createElement('div');
            time.className = 'log-time';
            time.textContent = new Date().toLocaleTimeString();
            
            const msg = document.createElement('div');
            msg.className = 'log-message';
            msg.textContent = message;
            
            entry.appendChild(time);
            entry.appendChild(msg);
            
            log.insertBefore(entry, log.firstChild);
            
            // Keep only last 50 entries
            while (log.children.length > 50) {
                log.removeChild(log.lastChild);
            }
        }
        
        function updateSystemTime() {
            // Update any time-based elements
        }
        
        function startActivitySimulation() {
            // Simulate system activity
            setInterval(() => {
                // Update metrics randomly
                const metrics = ['activeAgents', 'activeWorkers', 'taskQueue', 'memoryUsage'];
                const randomMetric = metrics[Math.floor(Math.random() * metrics.length)];
                const element = document.getElementById(randomMetric);
                
                if (element) {
                    let currentValue = parseInt(element.textContent);
                    let change = Math.floor(Math.random() * 10) - 5;
                    
                    if (randomMetric === 'memoryUsage') {
                        currentValue = Math.max(70, Math.min(99, currentValue + change));
                        element.textContent = currentValue + '%';
                    } else {
                        currentValue = Math.max(0, currentValue + change);
                        element.textContent = currentValue;
                    }
                }
                
                // Update charts
                if (charts.health) {
                    charts.health.data.datasets[0].data.shift();
                    charts.health.data.datasets[0].data.push(Math.random() * 20 + 80);
                    charts.health.update('none');
                }
                
                // Random activity log
                const activities = [
                    'Neural pathway optimized',
                    'Memory sync completed',
                    'Agent task redistributed',
                    'Worker health check passed',
                    'Database backup verified',
                    'AZR cycle completed',
                    'Task priority updated',
                    'Brain region synchronized',
                    'Connection stability verified'
                ];
                
                if (Math.random() > 0.8) {
                    logActivity(activities[Math.floor(Math.random() * activities.length)]);
                }
                
            }, 3000);
        }
        
        // Global Functions for Button Actions
        function showAllRegions() {
            logActivity('Highlighting all brain regions');
            // Highlight all regions briefly
            brainRegions.forEach((mesh, index) => {
                setTimeout(() => {
                    anime({
                        targets: mesh.material,
                        emissiveIntensity: 1.2,
                        duration: 500
                    });
                    anime({
                        targets: mesh.scale,
                        x: 1.2,
                        y: 1.2,
                        z: 1.2,
                        duration: 500,
                        complete: () => {
                            anime({
                                targets: mesh.material,
                                emissiveIntensity: 0.4,
                                duration: 500
                            });
                            anime({
                                targets: mesh.scale,
                                x: 1,
                                y: 1,
                                z: 1,
                                duration: 500
                            });
                        }
                    });
                }, index * 200);
            });
        }
        
        function runSystemDiagnostic() {
            logActivity('Running comprehensive system diagnostic...');
            setTimeout(() => {
                logActivity('System diagnostic completed - All systems operational');
            }, 2000);
        }
        
        function syncDatabase() {
            logActivity('Synchronizing database connections...');
            setTimeout(() => {
                logActivity('Database synchronization completed successfully');
            }, 1500);
        }
        
        function toggleAZRMode() {
            isAZRActive = !isAZRActive;
            const statusElement = document.querySelector('.azr-status');
            if (statusElement) {
                statusElement.textContent = isAZRActive ? 'AZR Cycle: ACTIVE' : 'AZR Cycle: READY';
            }
            logActivity(`AZR Mode ${isAZRActive ? 'activated' : 'deactivated'}`);
        }
        
        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('region-modal')) {
                event.target.classList.remove('active');
                document.body.style.overflow = 'auto';
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                // Close any open modals
                document.querySelectorAll('.region-modal.active').forEach(modal => {
                    modal.classList.remove('active');
                });
                document.body.style.overflow = 'auto';
            }
        });
    </script>
</body>
</html>