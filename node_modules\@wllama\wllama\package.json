{"name": "@wllama/wllama", "version": "2.3.2", "description": "WebAssembly binding for llama.cpp - Enabling on-browser LLM inference", "main": "index.js", "type": "module", "directories": {"example": "examples"}, "scripts": {"serve": "node ./scripts/http_server.js", "serve:mt": "MULTITHREAD=1 node ./scripts/http_server.js", "clean": "rm -rf ./esm && rm -rf ./docs && rm -rf ./wasm", "build:worker": "./scripts/build_worker.sh", "build:glue": "node ./cpp/generate_glue_prototype.js", "build:wasm": "./scripts/build_wasm.sh && npm run build:glue", "build:tsup": "tsup src/index.ts --format cjs,esm --clean", "build:minified": "terser esm/index.js -o esm/index.min.js --compress --mangle --source-map", "build:typedef": "tsc --emitDeclarationOnly --declaration -p tsconfig.build.json", "build": "npm run clean && npm run build:worker && npm run build:tsup && npm run build:minified && npm run build:typedef", "postbuild": "./scripts/post_build.sh && npm run docs", "docs": "typedoc --tsconfig tsconfig.build.json src/index.ts", "upload": "npm run format && npm run build && npm publish --access public", "format": "prettier --write .", "test": "vitest", "test:firefox": "BROWSER=firefox vitest", "test:safari": "BROWSER=safari vitest"}, "repository": {"type": "git", "url": "git+https://github.com/ngxson/wllama.git"}, "keywords": ["wasm", "webassembly", "llama", "llm", "ai", "rag", "embeddings", "generation"], "author": "<PERSON><PERSON>GUY<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ngxson/wllama/issues"}, "homepage": "https://github.com/ngxson/wllama#readme", "devDependencies": {"@vitest/browser": "^2.1.6", "express": "^4.18.3", "mime-types": "^2.1.35", "playwright": "^1.49.0", "prettier": "^3.3.3", "terser": "^5.39.0", "tsup": "^8.4.0", "typedoc": "^0.27.2", "typescript": "^5.4.2", "webdriverio": "^9.4.1"}, "prettier": {"trailingComma": "es5", "tabWidth": 2, "semi": true, "singleQuote": true, "bracketSameLine": false}}