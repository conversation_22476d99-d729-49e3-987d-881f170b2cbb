<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AZR Model Loading</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: #eee;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: #16213e;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #0f3460;
        }
        .test-result {
            background: #0f3460;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success { color: #1bf7cd; }
        .error { color: #e94560; }
        .warning { color: #f39c12; }
        .loading { color: #3498db; }
        .test-button {
            background: #e94560;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #d63447;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #0f3460;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1bf7cd, #3498db);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 AZR Model Loading Test</h1>
        <p>Testing your Absolute Zero Reasoner model from GitHub Pages</p>
        
        <div id="model-info">
            <h3>Model Information:</h3>
            <div class="test-result">
                <strong>Primary URL:</strong> https://4citeb4u.github.io/llama-models/andrewzh_Absolute_Zero_Reasoner-Coder-3b-Q4_K_M.gguf<br>
                <strong>Backup URL:</strong> https://raw.githubusercontent.com/4citeB4U/llama-models/main/andrewzh_Absolute_Zero_Reasoner-Coder-3b-Q4_K_M.gguf<br>
                <strong>Engine:</strong> Wllama (WebAssembly)
            </div>
        </div>
        
        <div id="progress-section" style="display: none;">
            <h3>Loading Progress:</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div id="progress-text">0%</div>
        </div>
        
        <div id="tests">
            <h3>Tests:</h3>
            <button class="test-button" onclick="testModelAccess()">Test Model Access</button>
            <button class="test-button" onclick="loadAZRModel()">Load AZR Model</button>
            <button class="test-button" onclick="testReasoning()">Test Reasoning</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="results">
            <h3>Test Results:</h3>
            <div id="test-output"></div>
        </div>
    </div>

    <!-- Load Wllama -->
    <script type="module">
        let wllama = null;
        let azrModel = null;
        
        // Import Wllama
        async function loadWllama() {
            try {
                addResult('🔄 Loading Wllama library...', 'loading');
                const { Wllama } = await import('https://cdn.jsdelivr.net/npm/@wllama/wllama@1.5.0/esm/index.js');
                
                const configPaths = {
                    'single-thread/wllama.wasm': 'https://cdn.jsdelivr.net/npm/@wllama/wllama@1.5.0/esm/single-thread/wllama.wasm',
                    'multi-thread/wllama.wasm': 'https://cdn.jsdelivr.net/npm/@wllama/wllama@1.5.0/esm/multi-thread/wllama.wasm',
                    'multi-thread/wllama.worker.mjs': 'https://cdn.jsdelivr.net/npm/@wllama/wllama@1.5.0/esm/multi-thread/wllama.worker.mjs',
                };
                
                wllama = new Wllama(configPaths);
                addResult('✅ Wllama library loaded successfully', 'success');
                return true;
            } catch (error) {
                addResult(`❌ Failed to load Wllama: ${error.message}`, 'error');
                return false;
            }
        }
        
        // Test model URL accessibility
        window.testModelAccess = async function() {
            addResult('🔄 Testing model URL accessibility...', 'loading');
            
            const urls = [
                'https://4citeb4u.github.io/llama-models/andrewzh_Absolute_Zero_Reasoner-Coder-3b-Q4_K_M.gguf',
                'https://raw.githubusercontent.com/4citeB4U/llama-models/main/andrewzh_Absolute_Zero_Reasoner-Coder-3b-Q4_K_M.gguf'
            ];
            
            for (const url of urls) {
                try {
                    addResult(`🔄 Testing: ${url}`, 'loading');
                    
                    const response = await fetch(url, { method: 'HEAD' });
                    if (response.ok) {
                        const size = response.headers.get('content-length');
                        const sizeText = size ? ` (${Math.round(size / 1024 / 1024)} MB)` : '';
                        addResult(`✅ Model accessible${sizeText}`, 'success');
                        addResult(`   URL: ${url}`, 'success');
                        return;
                    } else {
                        addResult(`❌ HTTP ${response.status}: ${response.statusText}`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ Network error: ${error.message}`, 'error');
                }
            }
            
            addResult('❌ No accessible model URLs found', 'error');
        };
        
        // Load AZR model
        window.loadAZRModel = async function() {
            if (!wllama) {
                const loaded = await loadWllama();
                if (!loaded) return;
            }
            
            addResult('🔄 Loading AZR model...', 'loading');
            document.getElementById('progress-section').style.display = 'block';
            
            const urls = [
                'https://4citeb4u.github.io/llama-models/andrewzh_Absolute_Zero_Reasoner-Coder-3b-Q4_K_M.gguf',
                'https://raw.githubusercontent.com/4citeB4U/llama-models/main/andrewzh_Absolute_Zero_Reasoner-Coder-3b-Q4_K_M.gguf'
            ];
            
            for (const url of urls) {
                try {
                    addResult(`🔄 Attempting to load from: ${url}`, 'loading');
                    
                    await wllama.loadModelFromUrl(url, {
                        progressCallback: (progress) => {
                            const percent = Math.round((progress.loaded / progress.total) * 100);
                            updateProgress(percent);
                            addResult(`📥 Loading: ${percent}%`, 'loading');
                        },
                        n_ctx: 4096,
                        n_threads: navigator.hardwareConcurrency || 4,
                        temperature: 0.1
                    });
                    
                    azrModel = wllama;
                    addResult('✅ AZR model loaded successfully!', 'success');
                    addResult(`   Model URL: ${url}`, 'success');
                    updateProgress(100);
                    return;
                    
                } catch (error) {
                    addResult(`❌ Failed to load from ${url}: ${error.message}`, 'error');
                }
            }
            
            addResult('❌ Failed to load AZR model from any URL', 'error');
            document.getElementById('progress-section').style.display = 'none';
        };
        
        // Test reasoning
        window.testReasoning = async function() {
            if (!azrModel) {
                addResult('❌ AZR model not loaded. Load the model first.', 'error');
                return;
            }
            
            addResult('🧠 Testing AZR reasoning...', 'loading');
            
            const testPrompt = `<reasoning>
You are the Absolute Zero Reasoner (AZR), an advanced AI system designed for deep logical reasoning and problem-solving.

Problem: What is 2+2 and explain your reasoning step by step?

Instructions:
1. Break down the problem into logical steps
2. Analyze each component systematically
3. Provide a clear, actionable solution

Reasoning Process:
`;
            
            try {
                const response = await azrModel.createCompletion(testPrompt, {
                    nPredict: 200,
                    temperature: 0.1,
                    topP: 0.9,
                    stopSequence: ['</reasoning>', 'Human:', 'User:']
                });
                
                addResult('✅ AZR reasoning test successful!', 'success');
                addResult(`🧠 Response: ${response}`, 'success');
                
            } catch (error) {
                addResult(`❌ Reasoning test failed: ${error.message}`, 'error');
            }
        };
        
        function addResult(message, type = '') {
            const output = document.getElementById('test-output');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
        }
        
        function updateProgress(percent) {
            const fill = document.getElementById('progress-fill');
            const text = document.getElementById('progress-text');
            fill.style.width = `${percent}%`;
            text.textContent = `${percent}%`;
        }
        
        window.clearResults = function() {
            document.getElementById('test-output').innerHTML = '';
            document.getElementById('progress-section').style.display = 'none';
            updateProgress(0);
        };
        
        // Auto-load Wllama on page load
        loadWllama();
    </script>
</body>
</html>
