# 🧠 Agent Lee Cognitive System - Quick Reference

## **Brain Hemisphere Color Map**
| Hemisphere | Color | Function | LLM | File Location |
|------------|-------|----------|-----|---------------|
| **PHI-3** | 🟡 Yellow | To-Do & Workflow | PHI-3 | `frontend/<PERSON>'s Dynamic To-Do List.html` |
| **GEMINI** | 🟠 Orange | Agent Operations | Gemini | `frontend/Agent Lee's Agent Center.html` |
| **QWEN** | 🔵 Teal | Worker Grid | Qwen | `frontend/Agent <PERSON>'s Integrated Workers Center.html` |
| **LLAMA** | 🔷 Sky Blue | Database Health | Llama | `frontend/Agent <PERSON>'s DB.html` |
| **ECHO** | 🔴 Red | System Logs | Echo | `frontend/Agent <PERSON>'s System Logs.html` |
| **AZR** | 🟣 Indigo | LLM Core/Brain Center | AZR | `frontend/Agent zlee's LLM Center.html` |

## **Core System Files**
```
📁 THEBESTAGENTLEE23/
├── 🧠 index.html                    # Main Cognitive Core Interface
├── 🗄️ database-manager.js           # Unified IndexedDB Operations
├── 🐍 azr_bridge.py                 # AZR WebSocket Backend
├── 📊 frontend/JS/
│   ├── cognitive-matrix.js          # 3D Brain Visualization
│   ├── real_llm_manager.js          # LLM Routing & Management
│   ├── working_echo_dispatcher.js   # Event Broadcasting
│   ├── automated_test_suite.js      # System Health Monitoring
│   └── gui_card_manager.js          # Floating Window Management
└── 📋 schema/                       # Database Schemas (22+ tables)
```

## **Key System Commands**

### **Start Agent Lee System:**
```bash
# 1. Start AZR Bridge (Python Backend)
python azr_bridge.py

# 2. Open Main Interface
# Open index.html in browser
```

### **Development Commands:**
```bash
# Install Python Dependencies
pip install llama-cpp-python websockets asyncio

# Build Distribution
./build_distribution.bat

# Run Tests
./run_azr_tests.bat
```

## **LLM Routing Logic**
- **Complex Planning/Reasoning** → AZR (Absolute Zero Reasoner)
- **Quick Tasks/Validation** → PHI-3
- **UI Echo Generation** → Gemini
- **Worker Management** → Qwen
- **General Purpose** → WebLLM/Llama

## **Event Broadcasting System**
```javascript
// Cross-hemisphere communication
window.dispatchEvent(new CustomEvent('hemisphereEvent', {
    detail: {
        hemisphere: 'phi3',
        eventType: 'task_completed',
        message: 'Task completed successfully',
        timestamp: new Date().toISOString()
    }
}));
```

## **Database Schema Quick Access**
- **AgentLeeDB** - Main IndexedDB database
- **22+ Tables** covering: LLM states, agent profiles, worker status, tasks, logs, UI actions
- **Version-controlled** with migration support
- **Local-first** with optional sync

## **Mobile Optimization**
- ✅ All features preserved on mobile
- ✅ Responsive sidebar navigation
- ✅ Touch-friendly floating windows
- ✅ Resizable Agent Lee avatar
- ✅ No feature degradation

## **Security Model**
- 🔒 **Local-first**: All data stored client-side by default
- 🔒 **WebSocket**: Only localhost connections for AZR bridge
- 🔒 **User consent**: Required for any external network calls
- 🔒 **Event isolation**: Cross-tab communication via BroadcastChannel

## **Automated Healing**
- 🔧 **Self-monitoring**: Continuous health checks
- 🔧 **Auto-restart**: Failed agents/workers automatically restarted
- 🔧 **Escalation**: Critical failures logged and escalated
- 🔧 **Diagnostics**: Real-time system health monitoring

## **Visual Feedback System**
- 🌟 **Neural Pulses**: Color-coded flashes for each hemisphere
- 🌟 **Activity Ticker**: Live feed of recent system events
- 🌟 **Status Indicators**: Real-time health and activity status
- 🌟 **3D Brain**: Interactive visualization of system state

## **Extension Points**
- 🔌 **New LLMs**: Add via `real_llm_manager.js`
- 🔌 **New Modules**: Create new hemisphere HTML files
- 🔌 **New Workers**: Extend worker grid system
- 🔌 **New Schemas**: Add to database schema directory

---

**For full details, see:** `DOCS/AGENT_LEE_SUPREME_OVERVIEW.md`
