/**
 * Streamlined LLM Manager for Agent Lee
 * Manages browser-ready LLMs with AZR integration
 */

class StreamlinedLLMManager {
    constructor() {
        this.llms = new Map();
        this.isInitialized = false;
        this.loadingProgress = new Map();
        this.hemisphereMapping = {
            'phi3': 'phi3',
            'llama': 'llama', 
            'gemma': 'gemini',
            'embedder': 'echo',
            'azr': 'azr'
        };
        this.initializationPromises = new Map();
        
        console.log('🧠 Streamlined LLM Manager initialized');
    }

    async initialize() {
        if (this.isInitialized) return;
        
        console.log('🚀 Initializing streamlined LLM system...');
        
        try {
            // Import all LLM modules
            const modules = await this.importLLMModules();
            
            // Register LLMs
            this.registerLLMs(modules);
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Initialize core models (AZR first, then others)
            await this.initializeCoreModels();
            
            this.isInitialized = true;
            console.log('✅ Streamlined LLM system initialized');
            
            // Dispatch ready event
            window.dispatchEvent(new CustomEvent('llmSystemReady', {
                detail: { manager: this, models: Array.from(this.llms.keys()) }
            }));
            
        } catch (error) {
            console.error('❌ Failed to initialize LLM system:', error);
            throw error;
        }
    }

    async importLLMModules() {
        console.log('📦 Importing LLM modules...');

        const modules = {};

        try {
            // Import all modules with error handling for each
            const modulePromises = [
                { name: 'phi3', path: './llm-modules/phi3.js', exportName: 'phi3LLM' },
                { name: 'llama', path: './llm-modules/llama.js', exportName: 'llamaLLM' },
                { name: 'gemma', path: './llm-modules/gemma.js', exportName: 'gemmaLLM' },
                { name: 'embedder', path: './llm-modules/embedder.js', exportName: 'embedderLLM' },
                { name: 'azr', path: './llm-modules/azr.js', exportName: 'azrLLM' }
            ];

            for (const moduleInfo of modulePromises) {
                try {
                    console.log(`📦 Importing ${moduleInfo.name}...`);
                    const module = await import(moduleInfo.path);
                    modules[moduleInfo.name] = module[moduleInfo.exportName];
                    console.log(`✅ ${moduleInfo.name} imported successfully`);
                } catch (error) {
                    console.warn(`⚠️ Failed to import ${moduleInfo.name}:`, error);
                    // Continue with other modules
                }
            }

            const loadedCount = Object.keys(modules).length;
            console.log(`✅ ${loadedCount}/5 LLM modules imported successfully`);
            return modules;

        } catch (error) {
            console.error('❌ Failed to import LLM modules:', error);
            throw error;
        }
    }

    registerLLMs(modules) {
        console.log('📋 Registering LLMs...');
        
        Object.entries(modules).forEach(([name, llm]) => {
            this.llms.set(name, llm);
            this.loadingProgress.set(name, 0);
            console.log(`✅ Registered ${name} LLM`);
        });
    }

    setupEventListeners() {
        // Listen for LLM loading progress
        window.addEventListener('llmLoadingProgress', (event) => {
            const { model, progress } = event.detail;
            this.loadingProgress.set(model, progress);
            
            // Update UI if available
            this.updateLoadingUI(model, progress);
        });
        
        // Listen for LLM ready events
        window.addEventListener('llmReady', (event) => {
            const { model, hemisphere } = event.detail;
            console.log(`✅ ${model} LLM ready (${hemisphere} hemisphere)`);
            
            // Flash hemisphere
            if (window.flashHemisphere && hemisphere) {
                window.flashHemisphere(hemisphere, `${model.toUpperCase()} LLM Ready`);
            }
        });
    }

    async initializeCoreModels() {
        console.log('🔄 Initializing core models...');
        
        // Initialize AZR first (most important)
        try {
            await this.initializeModel('azr');
        } catch (error) {
            console.warn('⚠️ AZR initialization failed, continuing with other models');
        }
        
        // Initialize other models in parallel
        const otherModels = ['phi3', 'gemma', 'llama', 'embedder'];
        const initPromises = otherModels.map(model => 
            this.initializeModel(model).catch(error => {
                console.warn(`⚠️ ${model} initialization failed:`, error);
                return null;
            })
        );
        
        await Promise.allSettled(initPromises);
        
        // Report initialization results
        this.reportInitializationResults();
    }

    async initializeModel(modelName) {
        if (this.initializationPromises.has(modelName)) {
            return this.initializationPromises.get(modelName);
        }
        
        const llm = this.llms.get(modelName);
        if (!llm) {
            throw new Error(`Model ${modelName} not found`);
        }
        
        console.log(`🔄 Initializing ${modelName}...`);
        
        const promise = llm.initialize();
        this.initializationPromises.set(modelName, promise);
        
        try {
            await promise;
            console.log(`✅ ${modelName} initialized successfully`);
            return llm;
        } catch (error) {
            console.error(`❌ Failed to initialize ${modelName}:`, error);
            throw error;
        }
    }

    reportInitializationResults() {
        const results = Array.from(this.llms.entries()).map(([name, llm]) => {
            const status = llm.getStatus();
            return {
                name,
                loaded: status.isLoaded,
                hemisphere: status.hemisphere,
                capabilities: status.capabilities
            };
        });
        
        const loadedCount = results.filter(r => r.loaded).length;
        const totalCount = results.length;
        
        console.log(`📊 LLM Initialization Complete: ${loadedCount}/${totalCount} models loaded`);
        results.forEach(result => {
            const status = result.loaded ? '✅' : '❌';
            console.log(`${status} ${result.name} (${result.hemisphere}): ${result.capabilities.join(', ')}`);
        });
        
        // Update activity ticker
        if (window.updateActivityTicker) {
            window.updateActivityTicker(`LLM System: ${loadedCount}/${totalCount} models ready`);
        }
    }

    updateLoadingUI(model, progress) {
        // Update model status in UI if available
        const statusElement = document.getElementById('model-status');
        if (statusElement) {
            statusElement.textContent = `Loading ${model}: ${progress}%`;
        }
    }

    async chat(message, modelName = 'phi3', context = []) {
        const llm = this.llms.get(modelName);
        if (!llm || !llm.isLoaded) {
            // Try fallback models
            const fallbackModel = this.findAvailableModel(['phi3', 'gemma', 'llama']);
            if (fallbackModel) {
                return await fallbackModel.chat(message, context);
            }
            throw new Error('No available chat models');
        }
        
        return await llm.chat(message, context);
    }

    async reason(problem, context = {}) {
        // Always try AZR first for reasoning
        const azr = this.llms.get('azr');
        if (azr && azr.isConnected) {
            try {
                return await azr.reason(problem, context);
            } catch (error) {
                console.warn('⚠️ AZR reasoning failed, using fallback');
            }
        }
        
        // Fallback to PHI-3 for reasoning
        const phi3 = this.llms.get('phi3');
        if (phi3 && phi3.isLoaded) {
            return await phi3.reason(problem, context.steps || []);
        }
        
        throw new Error('No available reasoning models');
    }

    async embed(text) {
        const embedder = this.llms.get('embedder');
        if (!embedder || !embedder.isLoaded) {
            throw new Error('Embedder model not available');
        }
        
        return await embedder.embed(text);
    }

    async semanticSearch(query, documents, topK = 5) {
        const embedder = this.llms.get('embedder');
        if (!embedder || !embedder.isLoaded) {
            throw new Error('Embedder model not available');
        }
        
        return await embedder.semanticSearch(query, documents, topK);
    }

    findAvailableModel(preferredOrder = []) {
        for (const modelName of preferredOrder) {
            const llm = this.llms.get(modelName);
            if (llm && llm.isLoaded) {
                return llm;
            }
        }
        
        // Find any available model
        for (const [name, llm] of this.llms) {
            if (llm.isLoaded) {
                return llm;
            }
        }
        
        return null;
    }

    getModelStatus(modelName) {
        const llm = this.llms.get(modelName);
        return llm ? llm.getStatus() : null;
    }

    getAllModelStatus() {
        const status = {};
        for (const [name, llm] of this.llms) {
            status[name] = llm.getStatus();
        }
        return status;
    }

    getAvailableModels() {
        return Array.from(this.llms.entries())
            .filter(([name, llm]) => llm.isLoaded)
            .map(([name, llm]) => ({
                name,
                hemisphere: this.hemisphereMapping[name],
                capabilities: llm.getStatus().capabilities
            }));
    }

    async routeToOptimalModel(task, taskType = 'chat') {
        const routing = {
            'reasoning': ['azr', 'phi3'],
            'chat': ['phi3', 'gemma', 'llama'],
            'embedding': ['embedder'],
            'database': ['llama'],
            'agents': ['gemma'],
            'summarization': ['gemma', 'phi3']
        };
        
        const preferredModels = routing[taskType] || ['phi3', 'gemma'];
        const availableModel = this.findAvailableModel(preferredModels);
        
        if (!availableModel) {
            throw new Error(`No available models for task type: ${taskType}`);
        }
        
        return availableModel;
    }
}

// Create global instance
window.StreamlinedLLMManager = new StreamlinedLLMManager();

// Initialize on load
document.addEventListener('DOMContentLoaded', () => {
    window.StreamlinedLLMManager.initialize().catch(error => {
        console.error('❌ Failed to initialize LLM system:', error);
    });
});

console.log('🧠 Streamlined LLM Manager loaded');
