
/**
 * Agent Lee Database Manager with Advanced Semantic Memory Layer
 * Integrates with existing AgentLeeDB IndexedDB structure
 * Provides sophisticated semantic memory, knowledge graphs, and contextual retrieval
 */

class AgentLeeSemanticMemoryLayer {
    constructor() {
        this.db = null;
        this.semanticGraph = new Map(); // Concept relationship graph
        this.contextVectors = new Map(); // Semantic embeddings cache
        this.memoryStrength = new Map(); // Memory consolidation weights
        this.associationMatrix = new Map(); // Concept-to-concept associations
        
        // Semantic memory configuration
        this.config = {
            maxSemanticNodes: 10000,
            associationThreshold: 0.3,
            consolidationInterval: 300000, // 5 minutes
            vectorDimensions: 384, // For semantic embeddings
            memoryDecayRate: 0.95,
            strengthThreshold: 0.7
        };
        
        this.initializeSemanticLayer();
    }

    async initializeSemanticLayer() {
        console.log('🧠 Initializing Agent Lee Semantic Memory Layer...');
        
        // Wait for AgentLeeDB to be available
        if (window.AgentLeeDB) {
            this.db = window.AgentLeeDB;
        } else {
            // Wait for database initialization
            window.addEventListener('AgentLeeDBReady', () => {
                this.db = window.AgentLeeDB;
                this.loadSemanticGraph();
            });
        }
        
        // Start semantic maintenance processes
        this.startSemanticMaintenance();
        
        console.log('✅ Semantic Memory Layer initialized');
    }

    // ===== SEMANTIC KNOWLEDGE STORAGE =====
    
    async storeSemanticKnowledge(concept, knowledge, context = {}) {
        const conceptId = this.generateConceptId(concept);
        const timestamp = new Date().toISOString();
        
        // Create semantic knowledge entry
        const semanticEntry = {
            concept_id: conceptId,
            concept_name: concept,
            knowledge_content: knowledge,
            context_tags: context.tags || [],
            confidence_score: context.confidence || 0.8,
            emotional_weight: context.emotion || 0.5,
            source_type: context.source || 'user_interaction',
            created_at: timestamp,
            last_accessed: timestamp,
            access_count: 0,
            strength_score: context.confidence || 0.8
        };
        
        try {
            // Store in memory_fragments table
            await this.addToDatabase('memory_fragments', {
                fragment_id: `semantic_${conceptId}_${Date.now()}`,
                memory_type: 'semantic',
                content: JSON.stringify(semanticEntry),
                relevance_score: semanticEntry.confidence_score,
                emotional_weight: semanticEntry.emotional_weight,
                last_accessed: timestamp,
                metadata: {
                    concept_id: conceptId,
                    concept_name: concept,
                    context_tags: semanticEntry.context_tags
                }
            });
            
            // Update semantic graph
            this.updateSemanticGraph(concept, knowledge, context);
            
            // Store in knowledge_updates for tracking
            await this.addToDatabase('knowledge_updates', {
                update_id: `semantic_update_${Date.now()}`,
                update_type: 'semantic_knowledge',
                source: semanticEntry.source_type,
                confidence: semanticEntry.confidence_score,
                timestamp: timestamp,
                content: JSON.stringify({
                    concept: concept,
                    knowledge: knowledge,
                    context: context
                })
            });
            
            console.log(`🧠 Stored semantic knowledge: ${concept}`);
            return { success: true, conceptId, entry: semanticEntry };
            
        } catch (error) {
            console.error('❌ Failed to store semantic knowledge:', error);
            return { success: false, error: error.message };
        }
    }

    // ===== SEMANTIC RETRIEVAL =====
    
    async retrieveSemanticKnowledge(query, options = {}) {
        const {
            limit = 10,
            minConfidence = 0.3,
            includeAssociations = true,
            contextFilter = null
        } = options;
        
        try {
            // Direct concept match
            const directMatches = await this.findDirectMatches(query, limit);
            
            // Semantic associations
            const associations = includeAssociations ? 
                await this.findSemanticAssociations(query, limit) : [];
            
            // Context-based retrieval
            const contextMatches = contextFilter ? 
                await this.findContextualMatches(query, contextFilter, limit) : [];
            
            // Combine and rank results
            const allResults = [...directMatches, ...associations, ...contextMatches];
            const rankedResults = this.rankSemanticResults(allResults, query);
            
            // Filter by confidence and limit
            const filteredResults = rankedResults
                .filter(result => result.confidence >= minConfidence)
                .slice(0, limit);
            
            // Update access patterns
            await this.updateAccessPatterns(filteredResults);
            
            console.log(`🧠 Retrieved ${filteredResults.length} semantic results for: ${query}`);
            return filteredResults;
            
        } catch (error) {
            console.error('❌ Semantic retrieval failed:', error);
            return [];
        }
    }

    async findDirectMatches(query, limit) {
        const queryLower = query.toLowerCase();
        const matches = [];
        
        try {
            // Search memory_fragments for semantic entries
            const transaction = this.db.transaction(['memory_fragments'], 'readonly');
            const store = transaction.objectStore('memory_fragments');
            const request = store.getAll();
            
            return new Promise((resolve) => {
                request.onsuccess = () => {
                    const fragments = request.result;
                    
                    for (const fragment of fragments) {
                        if (fragment.memory_type === 'semantic') {
                            try {
                                const content = JSON.parse(fragment.content);
                                const conceptName = content.concept_name?.toLowerCase() || '';
                                const knowledge = content.knowledge_content?.toLowerCase() || '';
                                
                                if (conceptName.includes(queryLower) || knowledge.includes(queryLower)) {
                                    matches.push({
                                        type: 'direct',
                                        concept: content.concept_name,
                                        knowledge: content.knowledge_content,
                                        confidence: content.confidence_score,
                                        relevance: this.calculateRelevance(query, content),
                                        timestamp: content.created_at,
                                        accessCount: content.access_count || 0,
                                        fragmentId: fragment.fragment_id
                                    });
                                }
                            } catch (parseError) {
                                console.warn('Failed to parse semantic fragment:', parseError);
                            }
                        }
                    }
                    
                    resolve(matches.slice(0, limit));
                };
            });
            
        } catch (error) {
            console.error('Direct match search failed:', error);
            return [];
        }
    }

    async findSemanticAssociations(query, limit) {
        const associations = [];
        const queryTerms = this.extractKeyTerms(query);
        
        // Search semantic graph for related concepts
        for (const term of queryTerms) {
            const relatedConcepts = this.semanticGraph.get(term.toLowerCase()) || [];
            
            for (const related of relatedConcepts) {
                if (related.strength >= this.config.associationThreshold) {
                    const knowledge = await this.getConceptKnowledge(related.concept);
                    if (knowledge) {
                        associations.push({
                            type: 'association',
                            concept: related.concept,
                            knowledge: knowledge.content,
                            confidence: related.strength,
                            relevance: related.strength * 0.8, // Slightly lower than direct matches
                            associationType: related.type,
                            sourceQuery: term
                        });
                    }
                }
            }
        }
        
        return associations.slice(0, limit);
    }

    // ===== SEMANTIC GRAPH MANAGEMENT =====
    
    updateSemanticGraph(concept, knowledge, context) {
        const conceptKey = concept.toLowerCase();
        const keyTerms = this.extractKeyTerms(knowledge);
        
        // Initialize concept node if not exists
        if (!this.semanticGraph.has(conceptKey)) {
            this.semanticGraph.set(conceptKey, []);
        }
        
        // Create associations with key terms
        for (const term of keyTerms) {
            if (term !== conceptKey) {
                this.addSemanticAssociation(conceptKey, term, {
                    strength: context.confidence || 0.8,
                    type: 'semantic',
                    timestamp: Date.now()
                });
            }
        }
        
        // Update memory strength
        const currentStrength = this.memoryStrength.get(conceptKey) || 0;
        const newStrength = Math.min(1.0, currentStrength + (context.confidence || 0.8) * 0.1);
        this.memoryStrength.set(conceptKey, newStrength);
    }

    addSemanticAssociation(concept1, concept2, properties) {
        // Add bidirectional association
        this.addDirectionalAssociation(concept1, concept2, properties);
        this.addDirectionalAssociation(concept2, concept1, properties);
    }

    addDirectionalAssociation(fromConcept, toConcept, properties) {
        if (!this.semanticGraph.has(fromConcept)) {
            this.semanticGraph.set(fromConcept, []);
        }
        
        const associations = this.semanticGraph.get(fromConcept);
        const existingIndex = associations.findIndex(a => a.concept === toConcept);
        
        if (existingIndex >= 0) {
            // Strengthen existing association
            associations[existingIndex].strength = Math.min(1.0, 
                associations[existingIndex].strength + properties.strength * 0.1);
            associations[existingIndex].lastUpdated = properties.timestamp;
        } else {
            // Create new association
            associations.push({
                concept: toConcept,
                strength: properties.strength,
                type: properties.type,
                created: properties.timestamp,
                lastUpdated: properties.timestamp
            });
        }
    }

    // ===== UTILITY METHODS =====
    
    generateConceptId(concept) {
        return concept.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 50);
    }

    extractKeyTerms(text) {
        const words = text.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3);
        
        // Remove common stop words
        const stopWords = new Set(['this', 'that', 'with', 'have', 'will', 'been', 'from', 'they', 'know', 'want', 'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them', 'well', 'were', 'what', 'your']);
        
        return [...new Set(words.filter(word => !stopWords.has(word)))];
    }

    calculateRelevance(query, content) {
        const queryTerms = this.extractKeyTerms(query);
        const contentTerms = this.extractKeyTerms(content.knowledge_content || '');
        
        const intersection = queryTerms.filter(term => contentTerms.includes(term));
        const union = [...new Set([...queryTerms, ...contentTerms])];
        
        return intersection.length / union.length; // Jaccard similarity
    }

    rankSemanticResults(results, query) {
        return results.sort((a, b) => {
            const scoreA = (a.relevance * 0.4) + (a.confidence * 0.4) + (a.accessCount * 0.1 / 100) + (a.type === 'direct' ? 0.1 : 0);
            const scoreB = (b.relevance * 0.4) + (b.confidence * 0.4) + (b.accessCount * 0.1 / 100) + (b.type === 'direct' ? 0.1 : 0);
            return scoreB - scoreA;
        });
    }

    async addToDatabase(storeName, data) {
        if (!this.db) throw new Error('Database not initialized');
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.add(data);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    startSemanticMaintenance() {
        // Periodic memory consolidation
        setInterval(() => {
            this.consolidateSemanticMemory();
        }, this.config.consolidationInterval);
        
        // Memory decay simulation
        setInterval(() => {
            this.applyMemoryDecay();
        }, 3600000); // Every hour
    }

    async consolidateSemanticMemory() {
        console.log('🧠 Consolidating semantic memory...');
        
        // Strengthen frequently accessed concepts
        for (const [concept, strength] of this.memoryStrength) {
            if (strength > this.config.strengthThreshold) {
                await this.reinforceSemanticConcept(concept);
            }
        }
        
        // Prune weak associations
        this.pruneWeakAssociations();
        
        console.log('✅ Semantic memory consolidation complete');
    }

    applyMemoryDecay() {
        // Gradually reduce strength of unused memories
        for (const [concept, strength] of this.memoryStrength) {
            const decayedStrength = strength * this.config.memoryDecayRate;
            if (decayedStrength < 0.1) {
                this.memoryStrength.delete(concept);
            } else {
                this.memoryStrength.set(concept, decayedStrength);
            }
        }
    }

    pruneWeakAssociations() {
        for (const [concept, associations] of this.semanticGraph) {
            const strongAssociations = associations.filter(
                assoc => assoc.strength >= this.config.associationThreshold
            );
            this.semanticGraph.set(concept, strongAssociations);
        }
    }
}

// Initialize global semantic memory layer
window.AgentLeeSemanticMemory = new AgentLeeSemanticMemoryLayer();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AgentLeeSemanticMemoryLayer;
}

console.log('🧠 Agent Lee Semantic Memory Layer loaded');

/**
 * Agent Lee Database Manager - Core Database Operations
 * Provides unified interface for all IndexedDB operations
 * Integrates with semantic memory layer and existing AgentLeeDB structure
 */

class AgentLeeDatabaseManager {
    constructor() {
        this.db = null;
        this.dbName = 'AgentLeeDB';
        this.dbVersion = 3;
        this.isInitialized = false;
        this.transactionQueue = [];
        this.schemas = new Map();

        // Database configuration
        this.config = {
            maxRetries: 3,
            retryDelay: 1000,
            transactionTimeout: 30000,
            batchSize: 100,
            cacheSize: 1000
        };

        // Initialize database connection
        this.initializeDatabase();
    }

    async initializeDatabase() {
        if (window.AgentLeeDB) {
            this.db = window.AgentLeeDB;
            this.isInitialized = true;
            console.log('✅ Database Manager connected to existing AgentLeeDB');
            return;
        }

        try {
            this.db = await this.openDatabase();
            this.isInitialized = true;
            window.AgentLeeDB = this.db;

            // Load database schemas
            await this.loadSchemas();

            // Process queued transactions
            await this.processTransactionQueue();

            console.log('✅ Agent Lee Database Manager initialized');

            // Dispatch ready event
            window.dispatchEvent(new CustomEvent('AgentLeeDBReady', {
                detail: { db: this.db, manager: this }
            }));

        } catch (error) {
            console.error('❌ Database initialization failed:', error);
            throw error;
        }
    }

    async openDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(request.result);

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                this.createObjectStores(db);
            };
        });
    }

    createObjectStores(db) {
        // Core Agent Lee object stores based on schema
        const stores = [
            { name: 'agents', keyPath: 'agent_id' },
            { name: 'agent_workflows', keyPath: 'workflow_id' },
            { name: 'tasks', keyPath: 'task_id' },
            { name: 'workers', keyPath: 'worker_id' },
            { name: 'llm_sessions', keyPath: 'session_id' },
            { name: 'azr_nodes', keyPath: 'node_id' },
            { name: 'execution_logs', keyPath: 'log_id' },
            { name: 'healing_logs', keyPath: 'healing_id' },
            { name: 'diagnostics', keyPath: 'diagnostic_id' },
            { name: 'gui_registry', keyPath: 'gui_id' },
            { name: 'telemetry', keyPath: 'entry_id' },
            { name: 'speech_styles', keyPath: 'style_id' },
            { name: 'engagement_signals', keyPath: 'signal_id' },
            { name: 'user_behavior', keyPath: 'behavior_id' },
            { name: 'memory_fragments', keyPath: 'fragment_id' },
            { name: 'learning_models', keyPath: 'model_id' },
            { name: 'local_db_refs', keyPath: 'ref_id' },
            { name: 'knowledge_updates', keyPath: 'update_id' },
            { name: 'motivation_triggers', keyPath: 'trigger_id' },
            { name: 'emotion_tracks', keyPath: 'emotion_id' },
            { name: 'agent_moods', keyPath: 'mood_id' },
            { name: 'conversation_history', keyPath: 'conversation_id' }
        ];

        for (const store of stores) {
            if (!db.objectStoreNames.contains(store.name)) {
                const objectStore = db.createObjectStore(store.name, { keyPath: store.keyPath });

                // Add common indexes
                objectStore.createIndex('timestamp', 'timestamp', { unique: false });
                objectStore.createIndex('created_at', 'created_at', { unique: false });

                // Store-specific indexes
                if (store.name === 'memory_fragments') {
                    objectStore.createIndex('memory_type', 'memory_type', { unique: false });
                    objectStore.createIndex('relevance_score', 'relevance_score', { unique: false });
                }

                if (store.name === 'llm_sessions') {
                    objectStore.createIndex('session_type', 'session_type', { unique: false });
                    objectStore.createIndex('model_name', 'model_name', { unique: false });
                }

                if (store.name === 'tasks') {
                    objectStore.createIndex('status', 'status', { unique: false });
                    objectStore.createIndex('priority', 'priority', { unique: false });
                }

                console.log(`✅ Created object store: ${store.name}`);
            }
        }
    }

    // ===== CORE DATABASE OPERATIONS =====

    async addRecord(storeName, record, options = {}) {
        if (!this.isInitialized) {
            return this.queueTransaction('add', storeName, record, options);
        }

        const { generateId = true, validateSchema = true } = options;

        try {
            // Generate ID if needed
            if (generateId && !this.hasValidId(storeName, record)) {
                record = this.generateRecordId(storeName, record);
            }

            // Validate against schema
            if (validateSchema) {
                const validation = this.validateRecord(storeName, record);
                if (!validation.valid) {
                    throw new Error(`Schema validation failed: ${validation.errors.join(', ')}`);
                }
            }

            // Add timestamp
            record.created_at = record.created_at || new Date().toISOString();
            record.updated_at = new Date().toISOString();

            const result = await this.executeTransaction(storeName, 'readwrite', (store) => {
                return store.add(record);
            });

            // Broadcast change event
            this.broadcastChange('add', storeName, record);

            console.log(`✅ Added record to ${storeName}:`, record);
            return { success: true, id: result, record };

        } catch (error) {
            console.error(`❌ Failed to add record to ${storeName}:`, error);
            return { success: false, error: error.message };
        }
    }

    async getRecord(storeName, id) {
        if (!this.isInitialized) {
            throw new Error('Database not initialized');
        }

        try {
            const result = await this.executeTransaction(storeName, 'readonly', (store) => {
                return store.get(id);
            });

            if (result) {
                console.log(`✅ Retrieved record from ${storeName}:`, id);
            }

            return result;

        } catch (error) {
            console.error(`❌ Failed to get record from ${storeName}:`, error);
            return null;
        }
    }

    async updateRecord(storeName, id, updates, options = {}) {
        if (!this.isInitialized) {
            return this.queueTransaction('update', storeName, { id, updates }, options);
        }

        try {
            // Get existing record
            const existingRecord = await this.getRecord(storeName, id);
            if (!existingRecord) {
                throw new Error(`Record not found: ${id}`);
            }

            // Merge updates
            const updatedRecord = {
                ...existingRecord,
                ...updates,
                updated_at: new Date().toISOString()
            };

            // Validate if requested
            if (options.validateSchema) {
                const validation = this.validateRecord(storeName, updatedRecord);
                if (!validation.valid) {
                    throw new Error(`Schema validation failed: ${validation.errors.join(', ')}`);
                }
            }

            const result = await this.executeTransaction(storeName, 'readwrite', (store) => {
                return store.put(updatedRecord);
            });

            // Broadcast change event
            this.broadcastChange('update', storeName, updatedRecord);

            console.log(`✅ Updated record in ${storeName}:`, id);
            return { success: true, record: updatedRecord };

        } catch (error) {
            console.error(`❌ Failed to update record in ${storeName}:`, error);
            return { success: false, error: error.message };
        }
    }

    async deleteRecord(storeName, id) {
        if (!this.isInitialized) {
            return this.queueTransaction('delete', storeName, id);
        }

        try {
            // Get record before deletion for event broadcasting
            const record = await this.getRecord(storeName, id);

            await this.executeTransaction(storeName, 'readwrite', (store) => {
                return store.delete(id);
            });

            // Broadcast change event
            this.broadcastChange('delete', storeName, { id, deletedRecord: record });

            console.log(`✅ Deleted record from ${storeName}:`, id);
            return { success: true };

        } catch (error) {
            console.error(`❌ Failed to delete record from ${storeName}:`, error);
            return { success: false, error: error.message };
        }
    }

    async queryRecords(storeName, options = {}) {
        if (!this.isInitialized) {
            throw new Error('Database not initialized');
        }

        const {
            index = null,
            range = null,
            direction = 'next',
            limit = null,
            filter = null
        } = options;

        try {
            const results = await this.executeTransaction(storeName, 'readonly', (store) => {
                return new Promise((resolve, reject) => {
                    const records = [];
                    let source = store;

                    if (index) {
                        source = store.index(index);
                    }

                    const request = range ? source.openCursor(range, direction) : source.openCursor(null, direction);

                    request.onsuccess = (event) => {
                        const cursor = event.target.result;
                        if (cursor) {
                            const record = cursor.value;

                            // Apply filter if provided
                            if (!filter || filter(record)) {
                                records.push(record);
                            }

                            // Check limit
                            if (limit && records.length >= limit) {
                                resolve(records);
                                return;
                            }

                            cursor.continue();
                        } else {
                            resolve(records);
                        }
                    };

                    request.onerror = () => reject(request.error);
                });
            });

            console.log(`✅ Queried ${results.length} records from ${storeName}`);
            return results;

        } catch (error) {
            console.error(`❌ Failed to query records from ${storeName}:`, error);
            return [];
        }
    }

    // ===== TRANSACTION MANAGEMENT =====

    async executeTransaction(storeName, mode, operation) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], mode);
            const store = transaction.objectStore(storeName);

            transaction.oncomplete = () => {
                // Transaction completed successfully
            };

            transaction.onerror = () => reject(transaction.error);
            transaction.onabort = () => reject(new Error('Transaction aborted'));

            try {
                const request = operation(store);

                if (request && request.onsuccess !== undefined) {
                    request.onsuccess = () => resolve(request.result);
                    request.onerror = () => reject(request.error);
                } else {
                    resolve(request);
                }

            } catch (error) {
                reject(error);
            }
        });
    }

    queueTransaction(operation, storeName, data, options = {}) {
        return new Promise((resolve, reject) => {
            this.transactionQueue.push({
                operation,
                storeName,
                data,
                options,
                resolve,
                reject
            });
        });
    }

    async processTransactionQueue() {
        console.log(`🔄 Processing ${this.transactionQueue.length} queued transactions...`);

        for (const transaction of this.transactionQueue) {
            try {
                let result;
                switch (transaction.operation) {
                    case 'add':
                        result = await this.addRecord(transaction.storeName, transaction.data, transaction.options);
                        break;
                    case 'update':
                        result = await this.updateRecord(transaction.storeName, transaction.data.id, transaction.data.updates, transaction.options);
                        break;
                    case 'delete':
                        result = await this.deleteRecord(transaction.storeName, transaction.data);
                        break;
                    default:
                        throw new Error(`Unknown operation: ${transaction.operation}`);
                }
                transaction.resolve(result);
            } catch (error) {
                transaction.reject(error);
            }
        }

        this.transactionQueue = [];
        console.log('✅ Transaction queue processed');
    }

    // ===== UTILITY METHODS =====

    hasValidId(storeName, record) {
        const keyPath = this.getKeyPath(storeName);
        return record[keyPath] !== undefined && record[keyPath] !== null;
    }

    generateRecordId(storeName, record) {
        const keyPath = this.getKeyPath(storeName);
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);

        record[keyPath] = `${storeName}_${timestamp}_${random}`;
        return record;
    }

    getKeyPath(storeName) {
        const keyPaths = {
            'agents': 'agent_id',
            'tasks': 'task_id',
            'workers': 'worker_id',
            'llm_sessions': 'session_id',
            'memory_fragments': 'fragment_id',
            'knowledge_updates': 'update_id'
        };

        return keyPaths[storeName] || `${storeName}_id`;
    }

    validateRecord(storeName, record) {
        // Basic validation - can be extended with schema definitions
        const keyPath = this.getKeyPath(storeName);

        if (!record[keyPath]) {
            return { valid: false, errors: [`Missing required field: ${keyPath}`] };
        }

        return { valid: true, errors: [] };
    }

    broadcastChange(action, storeName, data) {
        // Broadcast to AgentLee event system
        if (window.AgentLeeEventBus) {
            window.AgentLeeEventBus.postMessage({
                type: 'DatabaseChange',
                data: { action, storeName, data, timestamp: new Date().toISOString() }
            });
        }

        // Dispatch custom event
        window.dispatchEvent(new CustomEvent('AgentLeeDBChange', {
            detail: { action, storeName, data }
        }));
    }

    async loadSchemas() {
        // Load schema definitions if available
        if (window.AgentLee && window.AgentLee.Agent_Lee__Deep_Schema_Table) {
            for (const schema of window.AgentLee.Agent_Lee__Deep_Schema_Table) {
                this.schemas.set(schema['Object Store'], schema);
            }
            console.log(`✅ Loaded ${this.schemas.size} database schemas`);
        }
    }

    // ===== PUBLIC API =====

    async getStats() {
        const stats = {
            isInitialized: this.isInitialized,
            dbName: this.dbName,
            dbVersion: this.dbVersion,
            stores: {},
            totalRecords: 0
        };

        if (this.isInitialized) {
            for (const storeName of this.db.objectStoreNames) {
                try {
                    const count = await this.executeTransaction(storeName, 'readonly', (store) => {
                        return store.count();
                    });
                    stats.stores[storeName] = count;
                    stats.totalRecords += count;
                } catch (error) {
                    stats.stores[storeName] = 'error';
                }
            }
        }

        return stats;
    }

    async exportData(storeNames = null) {
        if (!this.isInitialized) {
            throw new Error('Database not initialized');
        }

        const stores = storeNames || Array.from(this.db.objectStoreNames);
        const exportData = {
            metadata: {
                exportDate: new Date().toISOString(),
                dbName: this.dbName,
                dbVersion: this.dbVersion
            },
            data: {}
        };

        for (const storeName of stores) {
            try {
                const records = await this.queryRecords(storeName);
                exportData.data[storeName] = records;
            } catch (error) {
                console.error(`Failed to export ${storeName}:`, error);
                exportData.data[storeName] = { error: error.message };
            }
        }

        return exportData;
    }
}

// Initialize global database manager
window.AgentLeeDatabaseManager = new AgentLeeDatabaseManager();

// Backward compatibility - expose helper methods
window.AgentLeeDBHelpers = {
    addRecord: (storeName, record) => window.AgentLeeDatabaseManager.addRecord(storeName, record),
    getRecord: (storeName, id) => window.AgentLeeDatabaseManager.getRecord(storeName, id),
    updateRecord: (storeName, id, updates) => window.AgentLeeDatabaseManager.updateRecord(storeName, id, updates),
    deleteRecord: (storeName, id) => window.AgentLeeDatabaseManager.deleteRecord(storeName, id),
    queryRecords: (storeName, options) => window.AgentLeeDatabaseManager.queryRecords(storeName, options),
    getStats: () => window.AgentLeeDatabaseManager.getStats(),
    exportData: (storeNames) => window.AgentLeeDatabaseManager.exportData(storeNames)
};

console.log('🗄️ Agent Lee Database Manager loaded');

// Database Management API
await window.AgentLeeDatabaseManager.addRecord('tasks', {
    task_name: "Learn semantic memory",
    priority: "high",
    status: "active"
});

const tasks = await window.AgentLeeDatabaseManager.queryRecords('tasks', {
    index: 'status',
    filter: (record) => record.priority === 'high'
});

